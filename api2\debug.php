<?php
/**
 * API Debug Script
 */

echo "<h1>API Debug Information</h1>\n";

// Check PHP version and extensions
echo "<h2>PHP Environment</h2>\n";
echo "<p><strong>PHP Version:</strong> " . PHP_VERSION . "</p>\n";
echo "<p><strong>Server Software:</strong> " . ($_SERVER['SERVER_SOFTWARE'] ?? 'Unknown') . "</p>\n";

// Check required extensions
$requiredExtensions = ['pdo', 'pdo_mysql', 'json', 'curl', 'session'];
echo "<h3>Required Extensions</h3>\n";
foreach ($requiredExtensions as $ext) {
    $loaded = extension_loaded($ext);
    echo "<p><strong>$ext:</strong> " . ($loaded ? '✅ Loaded' : '❌ Missing') . "</p>\n";
}

// Check file permissions
echo "<h2>File Permissions</h2>\n";
$files = [
    '.',
    'config.php',
    'index.php',
    'auth',
    'auth/index.php',
    '.htaccess'
];

foreach ($files as $file) {
    $path = __DIR__ . '/' . $file;
    if (file_exists($path)) {
        $perms = substr(sprintf('%o', fileperms($path)), -4);
        $readable = is_readable($path);
        echo "<p><strong>$file:</strong> $perms " . ($readable ? '✅ Readable' : '❌ Not readable') . "</p>\n";
    } else {
        echo "<p><strong>$file:</strong> ❌ Missing</p>\n";
    }
}

// Check configuration
echo "<h2>Configuration</h2>\n";
try {
    require_once __DIR__ . '/config.php';
    echo "<p>✅ Config loaded successfully</p>\n";
    echo "<p><strong>API Version:</strong> " . (defined('API_VERSION') ? API_VERSION : 'Not defined') . "</p>\n";
    echo "<p><strong>Base Path:</strong> " . (function_exists('getBasePath') ? getBasePath() : 'Function not available') . "</p>\n";
} catch (Exception $e) {
    echo "<p>❌ Config error: " . $e->getMessage() . "</p>\n";
}

// Check database connection
echo "<h2>Database Connection</h2>\n";
try {
    if (isset($database)) {
        echo "<p>✅ Database object available</p>\n";
        // Test a simple query
        $result = $database->query("SELECT 1 as test");
        if ($result) {
            echo "<p>✅ Database connection working</p>\n";
        } else {
            echo "<p>❌ Database query failed</p>\n";
        }
    } else {
        echo "<p>❌ Database object not available</p>\n";
    }
} catch (Exception $e) {
    echo "<p>❌ Database error: " . $e->getMessage() . "</p>\n";
}

// Check session
echo "<h2>Session</h2>\n";
echo "<p><strong>Session Status:</strong> " . session_status() . "</p>\n";
echo "<p><strong>Session ID:</strong> " . (session_id() ?: 'None') . "</p>\n";

// Check request information
echo "<h2>Request Information</h2>\n";
echo "<p><strong>Request Method:</strong> " . ($_SERVER['REQUEST_METHOD'] ?? 'Unknown') . "</p>\n";
echo "<p><strong>Request URI:</strong> " . ($_SERVER['REQUEST_URI'] ?? 'Unknown') . "</p>\n";
echo "<p><strong>Script Name:</strong> " . ($_SERVER['SCRIPT_NAME'] ?? 'Unknown') . "</p>\n";
echo "<p><strong>Query String:</strong> " . ($_SERVER['QUERY_STRING'] ?? 'None') . "</p>\n";

// Check headers
echo "<h2>Request Headers</h2>\n";
if (function_exists('getallheaders')) {
    $headers = getallheaders();
    foreach ($headers as $name => $value) {
        echo "<p><strong>$name:</strong> $value</p>\n";
    }
} else {
    echo "<p>getallheaders() function not available</p>\n";
}

// Test API functions
echo "<h2>API Functions Test</h2>\n";
try {
    if (function_exists('apiResponse')) {
        echo "<p>✅ apiResponse function available</p>\n";
    } else {
        echo "<p>❌ apiResponse function missing</p>\n";
    }
    
    if (function_exists('getRequestData')) {
        echo "<p>✅ getRequestData function available</p>\n";
        $data = getRequestData();
        echo "<p><strong>Request Data:</strong> " . json_encode($data) . "</p>\n";
    } else {
        echo "<p>❌ getRequestData function missing</p>\n";
    }
} catch (Exception $e) {
    echo "<p>❌ Function test error: " . $e->getMessage() . "</p>\n";
}

// Check error reporting
echo "<h2>Error Reporting</h2>\n";
echo "<p><strong>Error Reporting Level:</strong> " . error_reporting() . "</p>\n";
echo "<p><strong>Display Errors:</strong> " . (ini_get('display_errors') ? 'On' : 'Off') . "</p>\n";
echo "<p><strong>Log Errors:</strong> " . (ini_get('log_errors') ? 'On' : 'Off') . "</p>\n";
echo "<p><strong>Error Log:</strong> " . (ini_get('error_log') ?: 'Default') . "</p>\n";

?>
