<?php
/**
 * Wishlist API Endpoints
 * Mobile API for Flutter Application
 * Flix Salon & SPA - PHP Version
 */

require_once __DIR__ . '/../config.php';
require_once __DIR__ . '/../../includes/wishlist_functions.php';

// Parse the request path
$requestUri = $_SERVER['REQUEST_URI'];
$basePath = getBasePath() . '/api2/wishlist';

$path = parse_url($requestUri, PHP_URL_PATH);
if (strpos($path, $basePath) === 0) {
    $path = substr($path, strlen($basePath));
}

$path = ltrim($path, '/');
$segments = array_filter(explode('/', $path));

$method = $_SERVER['REQUEST_METHOD'];
$action = $segments[0] ?? '';

// Require customer authentication for all endpoints
$auth = authenticateApiRequest(true);
if ($auth['user_role'] !== 'CUSTOMER') {
    apiError('Customer access required', 403, 'CUSTOMER_ACCESS_REQUIRED');
}

$customerId = $auth['user_id'];

try {
    switch ($action) {
        case '':
            if ($method === 'GET') {
                // GET /wishlist - Get wishlist items
                handleGetWishlist($customerId);
            } elseif ($method === 'POST') {
                // POST /wishlist - Add to wishlist
                handleAddToWishlist($customerId);
            } else {
                apiError('Method not allowed', 405, 'METHOD_NOT_ALLOWED');
            }
            break;
            
        case 'toggle':
            // POST /wishlist/toggle - Toggle wishlist item
            if ($method !== 'POST') {
                apiError('Method not allowed', 405, 'METHOD_NOT_ALLOWED');
            }
            handleToggleWishlist($customerId);
            break;
            
        default:
            // DELETE /wishlist/{id} - Remove from wishlist
            if ($method !== 'DELETE') {
                apiError('Method not allowed', 405, 'METHOD_NOT_ALLOWED');
            }
            handleRemoveFromWishlist($customerId, $action);
            break;
    }
    
} catch (Exception $e) {
    error_log('Wishlist API Error: ' . $e->getMessage());
    apiError('Wishlist error', 500, 'WISHLIST_ERROR');
}

/**
 * Handle getting wishlist items
 */
function handleGetWishlist($customerId) {
    global $database;
    
    try {
        $limit = min((int)($_GET['limit'] ?? 50), 100);
        $offset = max((int)($_GET['offset'] ?? 0), 0);
        
        // Get wishlist items with service/package details
        $wishlistItems = $database->fetchAll("
            SELECT 
                w.id as wishlist_id,
                w.item_type,
                w.item_id,
                w.created_at,
                s.id as service_id,
                s.name as service_name,
                s.description as service_description,
                s.price as service_price,
                s.duration as service_duration,
                s.category as service_category,
                s.image as service_image,
                p.id as package_id,
                p.name as package_name,
                p.description as package_description,
                p.price as package_price,
                p.package_duration as package_duration,
                p.image as package_image
            FROM wishlist w
            LEFT JOIN services s ON w.item_type = 'service' AND w.item_id = s.id AND s.is_active = 1
            LEFT JOIN packages p ON w.item_type = 'package' AND w.item_id = p.id AND p.is_active = 1
            WHERE w.user_id = ?
            ORDER BY w.created_at DESC
            LIMIT ? OFFSET ?
        ", [$customerId, $limit, $offset]);
        
        // Get total count
        $totalCount = $database->fetch(
            "SELECT COUNT(*) as count FROM wishlist WHERE user_id = ?",
            [$customerId]
        )['count'];
        
        // Format wishlist items
        $formattedItems = [];
        foreach ($wishlistItems as $item) {
            if ($item['item_type'] === 'service' && $item['service_id']) {
                $formattedItem = [
                    'wishlist_id' => $item['wishlist_id'],
                    'type' => 'service',
                    'item' => formatServiceForApi([
                        'id' => $item['service_id'],
                        'name' => $item['service_name'],
                        'description' => $item['service_description'],
                        'price' => $item['service_price'],
                        'duration' => $item['service_duration'],
                        'category' => $item['service_category'],
                        'image' => $item['service_image'],
                        'is_active' => 1
                    ]),
                    'added_at' => $item['created_at']
                ];
            } elseif ($item['item_type'] === 'package' && $item['package_id']) {
                $formattedItem = [
                    'wishlist_id' => $item['wishlist_id'],
                    'type' => 'package',
                    'item' => formatPackageForApi([
                        'id' => $item['package_id'],
                        'name' => $item['package_name'],
                        'description' => $item['package_description'],
                        'price' => $item['package_price'],
                        'package_duration' => $item['package_duration'],
                        'image' => $item['package_image'],
                        'is_active' => 1
                    ]),
                    'added_at' => $item['created_at']
                ];
            } else {
                // Item no longer exists, skip it
                continue;
            }
            
            $formattedItems[] = $formattedItem;
        }
        
        apiResponse(true, [
            'wishlist_items' => $formattedItems,
            'pagination' => [
                'total' => (int)$totalCount,
                'limit' => $limit,
                'offset' => $offset,
                'has_more' => ($offset + $limit) < $totalCount
            ]
        ], 'Wishlist retrieved successfully');
        
    } catch (Exception $e) {
        error_log('Get wishlist error: ' . $e->getMessage());
        apiError('Failed to get wishlist', 500, 'GET_WISHLIST_ERROR');
    }
}

/**
 * Handle adding to wishlist
 */
function handleAddToWishlist($customerId) {
    try {
        $data = getRequestData();
        
        validateRequired($data, ['item_type', 'item_id']);
        
        $itemType = $data['item_type'];
        $itemId = $data['item_id'];
        
        if (!in_array($itemType, ['service', 'package'])) {
            apiError('Invalid item type. Must be "service" or "package"', 400, 'INVALID_ITEM_TYPE');
        }
        
        // Check if item exists and is active
        global $database;
        
        if ($itemType === 'service') {
            $item = $database->fetch(
                "SELECT id, name FROM services WHERE id = ? AND is_active = 1",
                [$itemId]
            );
        } else {
            $item = $database->fetch(
                "SELECT id, name FROM packages WHERE id = ? AND is_active = 1",
                [$itemId]
            );
        }
        
        if (!$item) {
            apiError('Item not found or inactive', 404, 'ITEM_NOT_FOUND');
        }
        
        // Check if already in wishlist
        $existing = $database->fetch(
            "SELECT id FROM wishlist WHERE user_id = ? AND item_type = ? AND item_id = ?",
            [$customerId, $itemType, $itemId]
        );
        
        if ($existing) {
            apiError('Item already in wishlist', 409, 'ALREADY_IN_WISHLIST');
        }
        
        // Add to wishlist
        $result = addToWishlist($customerId, $itemType, $itemId);
        
        if ($result['success']) {
            // Get updated wishlist count
            $count = getWishlistCount($customerId);
            
            apiResponse(true, [
                'wishlist_count' => $count,
                'item_name' => $item['name']
            ], 'Item added to wishlist successfully', 201);
        } else {
            apiError($result['message'], 400, 'ADD_WISHLIST_ERROR');
        }
        
    } catch (Exception $e) {
        error_log('Add to wishlist error: ' . $e->getMessage());
        apiError('Failed to add to wishlist', 500, 'ADD_WISHLIST_ERROR');
    }
}

/**
 * Handle toggling wishlist item
 */
function handleToggleWishlist($customerId) {
    try {
        $data = getRequestData();
        
        validateRequired($data, ['item_type', 'item_id']);
        
        $itemType = $data['item_type'];
        $itemId = $data['item_id'];
        
        if (!in_array($itemType, ['service', 'package'])) {
            apiError('Invalid item type. Must be "service" or "package"', 400, 'INVALID_ITEM_TYPE');
        }
        
        global $database;
        
        // Check if item exists in wishlist
        $existing = $database->fetch(
            "SELECT id FROM wishlist WHERE user_id = ? AND item_type = ? AND item_id = ?",
            [$customerId, $itemType, $itemId]
        );
        
        if ($existing) {
            // Remove from wishlist
            $result = removeFromWishlist($customerId, $itemType, $itemId);
            $action = 'removed';
        } else {
            // Add to wishlist
            $result = addToWishlist($customerId, $itemType, $itemId);
            $action = 'added';
        }
        
        if ($result['success']) {
            $count = getWishlistCount($customerId);
            
            apiResponse(true, [
                'action' => $action,
                'in_wishlist' => $action === 'added',
                'wishlist_count' => $count
            ], "Item $action successfully");
        } else {
            apiError($result['message'], 400, 'TOGGLE_WISHLIST_ERROR');
        }
        
    } catch (Exception $e) {
        error_log('Toggle wishlist error: ' . $e->getMessage());
        apiError('Failed to toggle wishlist', 500, 'TOGGLE_WISHLIST_ERROR');
    }
}

/**
 * Handle removing from wishlist
 */
function handleRemoveFromWishlist($customerId, $wishlistId) {
    global $database;
    
    if (empty($wishlistId)) {
        apiError('Wishlist ID is required', 400, 'MISSING_WISHLIST_ID');
    }
    
    try {
        // Check if wishlist item exists and belongs to user
        $wishlistItem = $database->fetch(
            "SELECT item_type, item_id FROM wishlist WHERE id = ? AND user_id = ?",
            [$wishlistId, $customerId]
        );
        
        if (!$wishlistItem) {
            apiError('Wishlist item not found', 404, 'WISHLIST_ITEM_NOT_FOUND');
        }
        
        // Remove from wishlist
        $database->execute(
            "DELETE FROM wishlist WHERE id = ? AND user_id = ?",
            [$wishlistId, $customerId]
        );
        
        // Get updated wishlist count
        $count = getWishlistCount($customerId);
        
        apiResponse(true, [
            'wishlist_count' => $count
        ], 'Item removed from wishlist successfully');
        
    } catch (Exception $e) {
        error_log('Remove from wishlist error: ' . $e->getMessage());
        apiError('Failed to remove from wishlist', 500, 'REMOVE_WISHLIST_ERROR');
    }
}
?>
