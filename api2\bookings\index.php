<?php
/**
 * Bookings API Endpoints
 * Mobile API for Flutter Application
 * Flix Salon & SPA - PHP Version
 */

require_once __DIR__ . '/../config.php';
require_once __DIR__ . '/../../includes/booking_functions.php';
require_once __DIR__ . '/../../includes/customer_panel_functions.php';

// Parse the request path
$requestUri = $_SERVER['REQUEST_URI'];
$basePath = getBasePath() . '/api2/bookings';

$path = parse_url($requestUri, PHP_URL_PATH);
if (strpos($path, $basePath) === 0) {
    $path = substr($path, strlen($basePath));
}

$path = ltrim($path, '/');
$segments = array_filter(explode('/', $path));

$method = $_SERVER['REQUEST_METHOD'];
$action = $segments[0] ?? '';

// Require customer authentication for all endpoints
$auth = authenticateApiRequest(true);
if ($auth['user_role'] !== 'CUSTOMER') {
    apiError('Customer access required', 403, 'CUSTOMER_ACCESS_REQUIRED');
}

$customerId = $auth['user_id'];

try {
    switch ($action) {
        case '':
            // POST /bookings - Create new booking
            if ($method !== 'POST') {
                apiError('Method not allowed', 405, 'METHOD_NOT_ALLOWED');
            }
            handleCreateBooking($customerId);
            break;
            
        case 'availability':
            // GET /bookings/availability - Check availability
            if ($method !== 'GET') {
                apiError('Method not allowed', 405, 'METHOD_NOT_ALLOWED');
            }
            handleCheckAvailability();
            break;
            
        case 'time-slots':
            // GET /bookings/time-slots - Get available time slots
            if ($method !== 'GET') {
                apiError('Method not allowed', 405, 'METHOD_NOT_ALLOWED');
            }
            handleGetTimeSlots();
            break;
            
        default:
            // Handle booking ID specific operations
            $bookingId = $action;
            
            if ($method === 'GET') {
                handleGetBooking($customerId, $bookingId);
            } elseif ($method === 'PUT') {
                handleUpdateBooking($customerId, $bookingId);
            } elseif ($method === 'DELETE') {
                handleCancelBooking($customerId, $bookingId);
            } else {
                apiError('Method not allowed', 405, 'METHOD_NOT_ALLOWED');
            }
            break;
    }
    
} catch (Exception $e) {
    error_log('Bookings API Error: ' . $e->getMessage());
    apiError('Booking error', 500, 'BOOKING_ERROR');
}

/**
 * Handle creating new booking
 */
function handleCreateBooking($customerId) {
    try {
        $data = getRequestData();
        
        // Validate required fields
        $required = ['date', 'start_time', 'staff_id'];
        
        // Either service_id or package_id is required
        if (empty($data['service_id']) && empty($data['package_id'])) {
            apiError('Either service_id or package_id is required', 400, 'MISSING_SERVICE_OR_PACKAGE');
        }
        
        validateRequired($data, $required);
        
        // Validate date format
        $date = DateTime::createFromFormat('Y-m-d', $data['date']);
        if (!$date || $date->format('Y-m-d') !== $data['date']) {
            apiError('Invalid date format. Use YYYY-MM-DD', 400, 'INVALID_DATE');
        }
        
        // Check if date is not in the past
        if ($date < new DateTime('today')) {
            apiError('Cannot book for past dates', 400, 'PAST_DATE');
        }
        
        // Validate time format
        if (!preg_match('/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/', $data['start_time'])) {
            apiError('Invalid time format. Use HH:MM', 400, 'INVALID_TIME');
        }
        
        // Prepare booking data
        $bookingData = [
            'user_id' => $customerId,
            'service_id' => $data['service_id'] ?? null,
            'service_variation_id' => $data['service_variation_id'] ?? null,
            'package_id' => $data['package_id'] ?? null,
            'staff_id' => $data['staff_id'],
            'date' => $data['date'],
            'start_time' => $data['start_time'],
            'notes' => $data['notes'] ?? '',
            'points_used' => (int)($data['points_used'] ?? 0)
        ];
        
        // Calculate end time and total amount
        global $database;
        
        if ($bookingData['service_id']) {
            $service = $database->fetch(
                "SELECT duration, price FROM services WHERE id = ? AND is_active = 1",
                [$bookingData['service_id']]
            );
            
            if (!$service) {
                apiError('Service not found', 404, 'SERVICE_NOT_FOUND');
            }
            
            $duration = (int)$service['duration'];
            $totalAmount = (float)$service['price'];
            
            // Check for service variation
            if ($bookingData['service_variation_id']) {
                $variation = $database->fetch(
                    "SELECT duration, price FROM service_variations WHERE id = ? AND service_id = ? AND is_active = 1",
                    [$bookingData['service_variation_id'], $bookingData['service_id']]
                );
                
                if ($variation) {
                    $duration = (int)$variation['duration'];
                    $totalAmount = (float)$variation['price'];
                }
            }
        } else {
            $package = $database->fetch(
                "SELECT package_duration, price FROM packages WHERE id = ? AND is_active = 1",
                [$bookingData['package_id']]
            );
            
            if (!$package) {
                apiError('Package not found', 404, 'PACKAGE_NOT_FOUND');
            }
            
            $duration = (int)$package['package_duration'];
            $totalAmount = (float)$package['price'];
            
            // If package duration is 0, calculate from services
            if ($duration === 0) {
                $serviceDuration = $database->fetch(
                    "SELECT SUM(s.duration) as total_duration 
                     FROM package_services ps 
                     JOIN services s ON ps.service_id = s.id 
                     WHERE ps.package_id = ? AND s.is_active = 1",
                    [$bookingData['package_id']]
                )['total_duration'];
                
                $duration = (int)$serviceDuration;
            }
        }
        
        // Calculate end time
        $startTime = DateTime::createFromFormat('H:i', $bookingData['start_time']);
        $endTime = clone $startTime;
        $endTime->add(new DateInterval('PT' . $duration . 'M'));
        $bookingData['end_time'] = $endTime->format('H:i');
        
        // Apply points discount
        $pointsUsed = min($bookingData['points_used'], floor($totalAmount / 10)); // 1 point = TSH 10
        $totalAmount -= ($pointsUsed * 10);
        $bookingData['total_amount'] = max(0, $totalAmount);
        $bookingData['points_used'] = $pointsUsed;
        
        // Check availability
        $isAvailable = checkBookingAvailability(
            $bookingData['staff_id'],
            $bookingData['date'],
            $bookingData['start_time'],
            $bookingData['end_time']
        );
        
        if (!$isAvailable) {
            apiError('Selected time slot is not available', 409, 'TIME_SLOT_UNAVAILABLE');
        }
        
        // Create booking
        $bookingId = createCustomerBooking($customerId, $bookingData);
        
        // Get created booking details
        $booking = $database->fetch("
            SELECT
                b.*,
                s.name as service_name,
                sv.name as variation_name,
                p.name as package_name,
                st.name as staff_name,
                st.phone as staff_phone
            FROM bookings b
            LEFT JOIN services s ON b.service_id = s.id
            LEFT JOIN service_variations sv ON b.service_variation_id = sv.id
            LEFT JOIN packages p ON b.package_id = p.id
            LEFT JOIN users st ON b.staff_id = st.id AND st.role = 'STAFF'
            WHERE b.id = ?
        ", [$bookingId]);
        
        $formattedBooking = formatBookingForApi($booking);
        
        apiResponse(true, [
            'booking' => $formattedBooking
        ], 'Booking created successfully', 201);
        
    } catch (Exception $e) {
        error_log('Create booking error: ' . $e->getMessage());
        apiError($e->getMessage(), 400, 'CREATE_BOOKING_ERROR');
    }
}

/**
 * Handle checking availability
 */
function handleCheckAvailability() {
    $staffId = $_GET['staff_id'] ?? '';
    $date = $_GET['date'] ?? '';
    $startTime = $_GET['start_time'] ?? '';
    $endTime = $_GET['end_time'] ?? '';
    
    if (empty($staffId) || empty($date) || empty($startTime) || empty($endTime)) {
        apiError('Missing required parameters: staff_id, date, start_time, end_time', 400, 'MISSING_PARAMS');
    }
    
    // Validate date format
    $dateObj = DateTime::createFromFormat('Y-m-d', $date);
    if (!$dateObj || $dateObj->format('Y-m-d') !== $date) {
        apiError('Invalid date format. Use YYYY-MM-DD', 400, 'INVALID_DATE');
    }
    
    // Validate time formats
    if (!preg_match('/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/', $startTime) ||
        !preg_match('/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/', $endTime)) {
        apiError('Invalid time format. Use HH:MM', 400, 'INVALID_TIME');
    }
    
    try {
        $isAvailable = checkBookingAvailability($staffId, $date, $startTime, $endTime);
        
        apiResponse(true, [
            'available' => $isAvailable,
            'staff_id' => $staffId,
            'date' => $date,
            'start_time' => $startTime,
            'end_time' => $endTime
        ], $isAvailable ? 'Time slot is available' : 'Time slot is not available');
        
    } catch (Exception $e) {
        error_log('Availability check error: ' . $e->getMessage());
        apiError('Failed to check availability', 500, 'AVAILABILITY_ERROR');
    }
}

/**
 * Handle getting available time slots
 */
function handleGetTimeSlots() {
    $staffId = $_GET['staff_id'] ?? '';
    $date = $_GET['date'] ?? '';
    $duration = (int)($_GET['duration'] ?? 60);
    
    if (empty($staffId) || empty($date)) {
        apiError('Missing required parameters: staff_id, date', 400, 'MISSING_PARAMS');
    }
    
    // Validate date format
    $dateObj = DateTime::createFromFormat('Y-m-d', $date);
    if (!$dateObj || $dateObj->format('Y-m-d') !== $date) {
        apiError('Invalid date format. Use YYYY-MM-DD', 400, 'INVALID_DATE');
    }
    
    try {
        // Get staff working hours (you may need to implement this)
        $workingHours = [
            'start' => '09:00',
            'end' => '18:00',
            'break_start' => '12:00',
            'break_end' => '13:00'
        ];
        
        // Generate time slots
        $timeSlots = [];
        $current = DateTime::createFromFormat('H:i', $workingHours['start']);
        $end = DateTime::createFromFormat('H:i', $workingHours['end']);
        $breakStart = DateTime::createFromFormat('H:i', $workingHours['break_start']);
        $breakEnd = DateTime::createFromFormat('H:i', $workingHours['break_end']);
        
        while ($current < $end) {
            $slotEnd = clone $current;
            $slotEnd->add(new DateInterval('PT' . $duration . 'M'));
            
            // Skip if slot goes beyond working hours
            if ($slotEnd > $end) {
                break;
            }
            
            // Skip break time
            if ($current >= $breakStart && $current < $breakEnd) {
                $current = clone $breakEnd;
                continue;
            }
            
            $startTime = $current->format('H:i');
            $endTime = $slotEnd->format('H:i');
            
            // Check availability
            $isAvailable = checkBookingAvailability($staffId, $date, $startTime, $endTime);
            
            $timeSlots[] = [
                'start_time' => $startTime,
                'end_time' => $endTime,
                'available' => $isAvailable
            ];
            
            // Move to next slot (30-minute intervals)
            $current->add(new DateInterval('PT30M'));
        }
        
        apiResponse(true, [
            'time_slots' => $timeSlots,
            'staff_id' => $staffId,
            'date' => $date,
            'duration' => $duration
        ], 'Time slots retrieved successfully');
        
    } catch (Exception $e) {
        error_log('Time slots error: ' . $e->getMessage());
        apiError('Failed to get time slots', 500, 'TIME_SLOTS_ERROR');
    }
}

/**
 * Handle getting single booking
 */
function handleGetBooking($customerId, $bookingId) {
    global $database;

    if (empty($bookingId)) {
        apiError('Booking ID is required', 400, 'MISSING_BOOKING_ID');
    }

    try {
        $booking = $database->fetch("
            SELECT
                b.*,
                s.name as service_name,
                sv.name as variation_name,
                p.name as package_name,
                st.name as staff_name,
                st.phone as staff_phone,
                pay.status as payment_status,
                pay.payment_gateway,
                pay.payment_reference
            FROM bookings b
            LEFT JOIN services s ON b.service_id = s.id
            LEFT JOIN service_variations sv ON b.service_variation_id = sv.id
            LEFT JOIN packages p ON b.package_id = p.id
            LEFT JOIN users st ON b.staff_id = st.id AND st.role = 'STAFF'
            LEFT JOIN payments pay ON b.id = pay.booking_id
            WHERE b.id = ? AND b.user_id = ?
        ", [$bookingId, $customerId]);

        if (!$booking) {
            apiError('Booking not found', 404, 'BOOKING_NOT_FOUND');
        }

        $formattedBooking = formatBookingForApi($booking);

        apiResponse(true, [
            'booking' => $formattedBooking
        ], 'Booking retrieved successfully');

    } catch (Exception $e) {
        error_log('Get booking error: ' . $e->getMessage());
        apiError('Failed to get booking', 500, 'GET_BOOKING_ERROR');
    }
}

/**
 * Handle updating booking (limited updates allowed)
 */
function handleUpdateBooking($customerId, $bookingId) {
    global $database;

    if (empty($bookingId)) {
        apiError('Booking ID is required', 400, 'MISSING_BOOKING_ID');
    }

    try {
        $data = getRequestData();

        // Get current booking
        $booking = $database->fetch(
            "SELECT * FROM bookings WHERE id = ? AND user_id = ?",
            [$bookingId, $customerId]
        );

        if (!$booking) {
            apiError('Booking not found', 404, 'BOOKING_NOT_FOUND');
        }

        // Only allow updates for pending bookings
        if ($booking['status'] !== 'PENDING') {
            apiError('Only pending bookings can be updated', 400, 'BOOKING_NOT_UPDATABLE');
        }

        // Only allow updating notes for now
        $allowedFields = ['notes'];
        $updateData = [];

        foreach ($allowedFields as $field) {
            if (isset($data[$field])) {
                $updateData[$field] = $data[$field];
            }
        }

        if (empty($updateData)) {
            apiError('No valid fields to update', 400, 'NO_UPDATE_FIELDS');
        }

        // Update booking
        $setClause = [];
        $params = [];

        foreach ($updateData as $field => $value) {
            $setClause[] = "$field = ?";
            $params[] = $value;
        }

        $params[] = $bookingId;
        $params[] = $customerId;

        $database->execute(
            "UPDATE bookings SET " . implode(', ', $setClause) . ", updated_at = NOW() WHERE id = ? AND user_id = ?",
            $params
        );

        // Get updated booking
        $updatedBooking = $database->fetch("
            SELECT
                b.*,
                s.name as service_name,
                sv.name as variation_name,
                p.name as package_name,
                st.name as staff_name,
                st.phone as staff_phone,
                pay.status as payment_status,
                pay.payment_gateway
            FROM bookings b
            LEFT JOIN services s ON b.service_id = s.id
            LEFT JOIN service_variations sv ON b.service_variation_id = sv.id
            LEFT JOIN packages p ON b.package_id = p.id
            LEFT JOIN users st ON b.staff_id = st.id AND st.role = 'STAFF'
            LEFT JOIN payments pay ON b.id = pay.booking_id
            WHERE b.id = ?
        ", [$bookingId]);

        $formattedBooking = formatBookingForApi($updatedBooking);

        apiResponse(true, [
            'booking' => $formattedBooking
        ], 'Booking updated successfully');

    } catch (Exception $e) {
        error_log('Update booking error: ' . $e->getMessage());
        apiError('Failed to update booking', 500, 'UPDATE_BOOKING_ERROR');
    }
}

/**
 * Handle canceling booking
 */
function handleCancelBooking($customerId, $bookingId) {
    global $database;

    if (empty($bookingId)) {
        apiError('Booking ID is required', 400, 'MISSING_BOOKING_ID');
    }

    try {
        // Get current booking
        $booking = $database->fetch(
            "SELECT * FROM bookings WHERE id = ? AND user_id = ?",
            [$bookingId, $customerId]
        );

        if (!$booking) {
            apiError('Booking not found', 404, 'BOOKING_NOT_FOUND');
        }

        // Check if booking can be cancelled
        $allowedStatuses = ['PENDING', 'CONFIRMED'];
        if (!in_array($booking['status'], $allowedStatuses)) {
            apiError('Booking cannot be cancelled', 400, 'BOOKING_NOT_CANCELLABLE');
        }

        // Check cancellation policy (e.g., 24 hours before)
        $bookingDateTime = DateTime::createFromFormat('Y-m-d H:i', $booking['date'] . ' ' . $booking['start_time']);
        $now = new DateTime();
        $hoursDifference = ($bookingDateTime->getTimestamp() - $now->getTimestamp()) / 3600;

        if ($hoursDifference < 24) {
            apiError('Bookings can only be cancelled at least 24 hours in advance', 400, 'CANCELLATION_TOO_LATE');
        }

        // Update booking status
        $result = updateBookingStatus($bookingId, 'CANCELLED', $customerId);

        if (!$result['success']) {
            apiError($result['error'], 400, 'CANCELLATION_FAILED');
        }

        // Get updated booking
        $updatedBooking = $database->fetch("
            SELECT
                b.*,
                s.name as service_name,
                sv.name as variation_name,
                p.name as package_name,
                st.name as staff_name
            FROM bookings b
            LEFT JOIN services s ON b.service_id = s.id
            LEFT JOIN service_variations sv ON b.service_variation_id = sv.id
            LEFT JOIN packages p ON b.package_id = p.id
            LEFT JOIN users st ON b.staff_id = st.id AND st.role = 'STAFF'
            WHERE b.id = ?
        ", [$bookingId]);

        $formattedBooking = formatBookingForApi($updatedBooking);

        apiResponse(true, [
            'booking' => $formattedBooking
        ], 'Booking cancelled successfully');

    } catch (Exception $e) {
        error_log('Cancel booking error: ' . $e->getMessage());
        apiError('Failed to cancel booking', 500, 'CANCEL_BOOKING_ERROR');
    }
}
