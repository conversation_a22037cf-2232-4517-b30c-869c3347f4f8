<?php
/**
 * API2 Main Router
 * Mobile API for Flutter Application
 * Flix Salon & SPA - PHP Version
 */

require_once __DIR__ . '/config.php';

// Log the request
logApiRequest($_SERVER['REQUEST_URI'], $_SERVER['REQUEST_METHOD'], getRequestData());

// Parse the request path
$requestUri = $_SERVER['REQUEST_URI'];
$basePath = getBasePath() . '/api2';

// Remove base path and query string
$path = parse_url($requestUri, PHP_URL_PATH);
if (strpos($path, $basePath) === 0) {
    $path = substr($path, strlen($basePath));
}

// Remove leading slash
$path = ltrim($path, '/');

// Split path into segments
$segments = array_filter(explode('/', $path));

// If no segments, show API info
if (empty($segments)) {
    apiResponse(true, [
        'name' => 'Flix Salon & SPA Mobile API',
        'version' => API_VERSION,
        'endpoints' => [
            'auth' => [
                'POST /auth/login' => 'User login',
                'POST /auth/register' => 'User registration',
                'POST /auth/logout' => 'User logout',
                'GET /auth/validate' => 'Validate session',
                'POST /auth/forgot-password' => 'Password reset request'
            ],
            'services' => [
                'GET /services' => 'List all services',
                'GET /services/{id}' => 'Get service details',
                'GET /services/categories' => 'Get service categories',
                'GET /services/search' => 'Search services'
            ],
            'packages' => [
                'GET /packages' => 'List all packages',
                'GET /packages/{id}' => 'Get package details',
                'GET /packages/search' => 'Search packages'
            ],
            'customer' => [
                'GET /customer/dashboard' => 'Customer dashboard data',
                'GET /customer/profile' => 'Customer profile',
                'PUT /customer/profile' => 'Update customer profile',
                'GET /customer/bookings' => 'Customer booking history',
                'GET /customer/points' => 'Customer points and rewards',
                'GET /customer/stats' => 'Customer statistics'
            ],
            'bookings' => [
                'POST /bookings' => 'Create new booking',
                'GET /bookings/{id}' => 'Get booking details',
                'PUT /bookings/{id}' => 'Update booking',
                'DELETE /bookings/{id}' => 'Cancel booking',
                'GET /bookings/availability' => 'Check availability'
            ],
            'wishlist' => [
                'GET /wishlist' => 'Get wishlist items',
                'POST /wishlist' => 'Add to wishlist',
                'DELETE /wishlist/{id}' => 'Remove from wishlist'
            ]
        ]
    ], 'Flix Salon & SPA Mobile API v' . API_VERSION);
}

// Route to appropriate endpoint
$endpoint = $segments[0];

try {
    switch ($endpoint) {
        case 'auth':
            require_once __DIR__ . '/auth/index.php';
            break;
            
        case 'services':
            require_once __DIR__ . '/services/index.php';
            break;
            
        case 'packages':
            require_once __DIR__ . '/packages/index.php';
            break;
            
        case 'customer':
            require_once __DIR__ . '/customer/index.php';
            break;
            
        case 'bookings':
            require_once __DIR__ . '/bookings/index.php';
            break;
            
        case 'wishlist':
            require_once __DIR__ . '/wishlist/index.php';
            break;
            
        default:
            apiError('Endpoint not found', 404, 'ENDPOINT_NOT_FOUND');
            break;
    }
    
} catch (Exception $e) {
    error_log('API2 Error: ' . $e->getMessage());
    apiError('Internal server error', 500, 'INTERNAL_ERROR');
}
?>
