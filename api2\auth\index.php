<?php
/**
 * Authentication API Endpoints
 * Mobile API for Flutter Application
 * Flix Salon & SPA - PHP Version
 */

require_once __DIR__ . '/../config.php';
require_once __DIR__ . '/../../includes/auth.php';

// Parse the request path
$requestUri = $_SERVER['REQUEST_URI'];
$basePath = getBasePath() . '/api2/auth';

$path = parse_url($requestUri, PHP_URL_PATH);
if (strpos($path, $basePath) === 0) {
    $path = substr($path, strlen($basePath));
}

$path = ltrim($path, '/');
$segments = array_filter(explode('/', $path));

$method = $_SERVER['REQUEST_METHOD'];
$action = $segments[0] ?? '';

// Initialize auth class
$auth = new Auth($database);

try {
    switch ($action) {
        case 'login':
            if ($method !== 'POST') {
                apiError('Method not allowed', 405, 'METHOD_NOT_ALLOWED');
            }
            handleLogin($auth);
            break;
            
        case 'register':
            if ($method !== 'POST') {
                apiError('Method not allowed', 405, 'METHOD_NOT_ALLOWED');
            }
            handleRegister($auth);
            break;
            
        case 'logout':
            if ($method !== 'POST') {
                apiError('Method not allowed', 405, 'METHOD_NOT_ALLOWED');
            }
            handleLogout($auth);
            break;
            
        case 'validate':
            if ($method !== 'GET') {
                apiError('Method not allowed', 405, 'METHOD_NOT_ALLOWED');
            }
            handleValidateSession();
            break;
            
        case 'forgot-password':
            if ($method !== 'POST') {
                apiError('Method not allowed', 405, 'METHOD_NOT_ALLOWED');
            }
            handleForgotPassword();
            break;
            
        default:
            apiError('Auth endpoint not found', 404, 'ENDPOINT_NOT_FOUND');
            break;
    }
    
} catch (Exception $e) {
    error_log('Auth API Error: ' . $e->getMessage());
    apiError('Authentication error', 500, 'AUTH_ERROR');
}

/**
 * Handle user login
 */
function handleLogin($auth) {
    $data = getRequestData();
    
    validateRequired($data, ['email', 'password']);
    
    $email = sanitize($data['email']);
    $password = $data['password'];
    
    if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        apiError('Invalid email format', 400, 'INVALID_EMAIL');
    }
    
    $result = $auth->login($email, $password);
    
    if ($result['success']) {
        $user = $result['user'];
        
        // Only allow customers to use mobile API
        if ($user['role'] !== 'CUSTOMER') {
            $auth->logout();
            apiError('Mobile app is only available for customers', 403, 'INVALID_ROLE');
        }
        
        apiResponse(true, [
            'user' => [
                'id' => $user['id'],
                'name' => $user['name'],
                'email' => $user['email'],
                'role' => $user['role']
            ],
            'session' => [
                'token' => $_SESSION['session_token'] ?? null,
                'expires' => date('c', strtotime('+2 weeks'))
            ]
        ], 'Login successful');
    } else {
        apiError($result['error'], 401, 'LOGIN_FAILED');
    }
}

/**
 * Handle user registration
 */
function handleRegister($auth) {
    $data = getRequestData();
    
    validateRequired($data, ['name', 'email', 'password', 'confirm_password']);
    
    if ($data['password'] !== $data['confirm_password']) {
        apiError('Passwords do not match', 400, 'PASSWORD_MISMATCH');
    }
    
    if (!filter_var($data['email'], FILTER_VALIDATE_EMAIL)) {
        apiError('Invalid email format', 400, 'INVALID_EMAIL');
    }
    
    if (strlen($data['password']) < 6) {
        apiError('Password must be at least 6 characters', 400, 'PASSWORD_TOO_SHORT');
    }
    
    $result = $auth->register($data);
    
    if ($result['success']) {
        // Auto-login after successful registration
        $loginResult = $auth->login($data['email'], $data['password']);
        
        if ($loginResult['success']) {
            $user = $loginResult['user'];
            
            apiResponse(true, [
                'user' => [
                    'id' => $user['id'],
                    'name' => $user['name'],
                    'email' => $user['email'],
                    'role' => $user['role']
                ],
                'session' => [
                    'token' => $_SESSION['session_token'] ?? null,
                    'expires' => date('c', strtotime('+2 weeks'))
                ]
            ], 'Registration successful');
        } else {
            apiResponse(true, null, 'Registration successful. Please login.');
        }
    } else {
        $errors = $result['errors'];
        $errorMessage = is_array($errors) ? implode(', ', $errors) : $errors;
        apiError($errorMessage, 400, 'REGISTRATION_FAILED');
    }
}

/**
 * Handle user logout
 */
function handleLogout($auth) {
    $authData = authenticateApiRequest(true);
    
    $auth->logout();
    
    apiResponse(true, null, 'Logout successful');
}

/**
 * Handle session validation
 */
function handleValidateSession() {
    $authData = authenticateApiRequest(false);
    
    if ($authData['authenticated']) {
        // Get fresh user data
        global $database;
        $user = $database->fetch(
            "SELECT id, name, email, role, points FROM users WHERE id = ?",
            [$authData['user_id']]
        );
        
        if ($user && $user['role'] === 'CUSTOMER') {
            apiResponse(true, [
                'valid' => true,
                'user' => [
                    'id' => $user['id'],
                    'name' => $user['name'],
                    'email' => $user['email'],
                    'role' => $user['role'],
                    'points' => (int)$user['points']
                ],
                'session' => [
                    'token' => $_SESSION['session_token'] ?? null,
                    'last_activity' => date('c', $_SESSION['last_activity'] ?? time())
                ]
            ], 'Session is valid');
        } else {
            apiResponse(true, ['valid' => false], 'Invalid user or role');
        }
    } else {
        apiResponse(true, ['valid' => false], 'Session not found or expired');
    }
}

/**
 * Handle forgot password
 */
function handleForgotPassword() {
    $data = getRequestData();
    
    validateRequired($data, ['email']);
    
    $email = sanitize($data['email']);
    
    if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        apiError('Invalid email format', 400, 'INVALID_EMAIL');
    }
    
    global $database;
    
    // Check if user exists
    $user = $database->fetch(
        "SELECT id, name FROM users WHERE email = ? AND role = 'CUSTOMER'",
        [$email]
    );
    
    if (!$user) {
        // Don't reveal if email exists or not for security
        apiResponse(true, null, 'If the email exists, a password reset link has been sent.');
        return;
    }
    
    // Generate reset token
    $resetToken = bin2hex(random_bytes(32));
    $expires = date('Y-m-d H:i:s', strtotime('+1 hour'));
    
    // Store reset token (you may need to create this table)
    try {
        $database->execute(
            "INSERT INTO password_resets (email, token, expires, created_at) VALUES (?, ?, ?, NOW())
             ON DUPLICATE KEY UPDATE token = VALUES(token), expires = VALUES(expires), created_at = NOW()",
            [$email, $resetToken, $expires]
        );
        
        // TODO: Send email with reset link
        // For now, just return success
        apiResponse(true, [
            'reset_token' => $resetToken // Remove this in production
        ], 'Password reset link has been sent to your email.');
        
    } catch (Exception $e) {
        error_log('Password reset error: ' . $e->getMessage());
        apiResponse(true, null, 'If the email exists, a password reset link has been sent.');
    }
}
?>
