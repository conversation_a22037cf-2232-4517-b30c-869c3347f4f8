<?php
/**
 * Packages API Endpoints
 * Mobile API for Flutter Application
 * Flix Salon & SPA - PHP Version
 */

require_once __DIR__ . '/../config.php';

// Parse the request path
$requestUri = $_SERVER['REQUEST_URI'];
$basePath = getBasePath() . '/api2/packages';

$path = parse_url($requestUri, PHP_URL_PATH);
if (strpos($path, $basePath) === 0) {
    $path = substr($path, strlen($basePath));
}

$path = ltrim($path, '/');
$segments = array_filter(explode('/', $path));

$method = $_SERVER['REQUEST_METHOD'];
$action = $segments[0] ?? '';

try {
    switch ($action) {
        case '':
            // GET /packages - List all packages
            if ($method !== 'GET') {
                apiError('Method not allowed', 405, 'METHOD_NOT_ALLOWED');
            }
            handleListPackages();
            break;
            
        case 'search':
            // GET /packages/search - Search packages
            if ($method !== 'GET') {
                apiError('Method not allowed', 405, 'METHOD_NOT_ALLOWED');
            }
            handleSearchPackages();
            break;
            
        default:
            // GET /packages/{id} - Get package details
            if ($method !== 'GET') {
                apiError('Method not allowed', 405, 'METHOD_NOT_ALLOWED');
            }
            handleGetPackage($action);
            break;
    }
    
} catch (Exception $e) {
    error_log('Packages API Error: ' . $e->getMessage());
    apiError('Packages error', 500, 'PACKAGES_ERROR');
}

/**
 * Handle listing all packages
 */
function handleListPackages() {
    global $database;
    
    // Get query parameters
    $limit = min((int)($_GET['limit'] ?? 50), 100); // Max 100 items
    $offset = max((int)($_GET['offset'] ?? 0), 0);
    $sortBy = $_GET['sort_by'] ?? 'price';
    $sortOrder = strtoupper($_GET['sort_order'] ?? 'ASC') === 'DESC' ? 'DESC' : 'ASC';
    
    // Validate sort field
    $allowedSortFields = ['name', 'price', 'package_duration', 'created_at'];
    if (!in_array($sortBy, $allowedSortFields)) {
        $sortBy = 'price';
    }
    
    // Get total count
    $totalCount = $database->fetch(
        "SELECT COUNT(*) as count FROM packages WHERE is_active = 1"
    )['count'];
    
    // Get packages with calculated fields
    $packages = $database->fetchAll("
        SELECT
            p.id,
            p.name,
            p.description,
            p.price,
            p.image,
            p.package_duration,
            p.is_active,
            p.created_at,
            p.updated_at,
            COALESCE(SUM(s.price), 0) as original_price,
            COALESCE(SUM(s.duration), 0) as calculated_duration,
            COUNT(ps.service_id) as service_count
        FROM packages p
        LEFT JOIN package_services ps ON p.id = ps.package_id
        LEFT JOIN services s ON ps.service_id = s.id AND s.is_active = 1
        WHERE p.is_active = 1
        GROUP BY p.id, p.name, p.description, p.price, p.image, p.package_duration, p.is_active, p.created_at, p.updated_at
        ORDER BY p.$sortBy $sortOrder
        LIMIT $limit OFFSET $offset
    ");
    
    // Format packages for API
    $formattedPackages = [];
    foreach ($packages as $package) {
        $formattedPackage = formatPackageForApi($package);
        
        // Calculate additional fields
        $originalPrice = (float)$package['original_price'];
        $packagePrice = (float)$package['price'];
        
        if ($originalPrice > 0 && $originalPrice > $packagePrice) {
            $formattedPackage['savings'] = $originalPrice - $packagePrice;
            $formattedPackage['discount_percentage'] = round((($originalPrice - $packagePrice) / $originalPrice) * 100);
        } else {
            $formattedPackage['savings'] = 0;
            $formattedPackage['discount_percentage'] = 0;
        }
        
        // Use package_duration if set, otherwise use calculated duration
        $formattedPackage['duration'] = $package['package_duration'] > 0 ? 
            (int)$package['package_duration'] : (int)$package['calculated_duration'];
        
        $formattedPackages[] = $formattedPackage;
    }
    
    apiResponse(true, [
        'packages' => $formattedPackages,
        'pagination' => [
            'total' => (int)$totalCount,
            'limit' => $limit,
            'offset' => $offset,
            'has_more' => ($offset + $limit) < $totalCount
        ]
    ], 'Packages retrieved successfully');
}

/**
 * Handle searching packages
 */
function handleSearchPackages() {
    global $database;
    
    $query = $_GET['q'] ?? '';
    $minPrice = $_GET['min_price'] ?? '';
    $maxPrice = $_GET['max_price'] ?? '';
    $maxDuration = $_GET['max_duration'] ?? '';
    $limit = min((int)($_GET['limit'] ?? 20), 50);
    $offset = max((int)($_GET['offset'] ?? 0), 0);
    
    if (empty($query) && empty($minPrice) && empty($maxPrice) && empty($maxDuration)) {
        apiError('At least one search parameter is required', 400, 'MISSING_SEARCH_PARAMS');
    }
    
    $whereClause = "WHERE p.is_active = 1";
    $params = [];
    
    if ($query) {
        $whereClause .= " AND (p.name LIKE ? OR p.description LIKE ?)";
        $params[] = "%$query%";
        $params[] = "%$query%";
    }
    
    if ($minPrice !== '' && shouldShowPricing()) {
        $whereClause .= " AND p.price >= ?";
        $params[] = floatval($minPrice);
    }
    
    if ($maxPrice !== '' && shouldShowPricing()) {
        $whereClause .= " AND p.price <= ?";
        $params[] = floatval($maxPrice);
    }
    
    if ($maxDuration !== '') {
        $whereClause .= " AND (p.package_duration <= ? OR (p.package_duration = 0 AND COALESCE(SUM(s.duration), 0) <= ?))";
        $params[] = intval($maxDuration);
        $params[] = intval($maxDuration);
    }
    
    // Get packages with search filters
    $packages = $database->fetchAll("
        SELECT
            p.id,
            p.name,
            p.description,
            p.price,
            p.image,
            p.package_duration,
            p.is_active,
            p.created_at,
            COALESCE(SUM(s.price), 0) as original_price,
            COALESCE(SUM(s.duration), 0) as calculated_duration,
            COUNT(ps.service_id) as service_count
        FROM packages p
        LEFT JOIN package_services ps ON p.id = ps.package_id
        LEFT JOIN services s ON ps.service_id = s.id AND s.is_active = 1
        $whereClause
        GROUP BY p.id, p.name, p.description, p.price, p.image, p.package_duration, p.is_active, p.created_at
        ORDER BY p.name ASC
        LIMIT $limit OFFSET $offset
    ", $params);
    
    // Format packages for API
    $formattedPackages = [];
    foreach ($packages as $package) {
        $formattedPackage = formatPackageForApi($package);
        
        // Calculate additional fields
        $originalPrice = (float)$package['original_price'];
        $packagePrice = (float)$package['price'];
        
        if ($originalPrice > 0 && $originalPrice > $packagePrice) {
            $formattedPackage['savings'] = $originalPrice - $packagePrice;
            $formattedPackage['discount_percentage'] = round((($originalPrice - $packagePrice) / $originalPrice) * 100);
        } else {
            $formattedPackage['savings'] = 0;
            $formattedPackage['discount_percentage'] = 0;
        }
        
        $formattedPackage['duration'] = $package['package_duration'] > 0 ? 
            (int)$package['package_duration'] : (int)$package['calculated_duration'];
        
        $formattedPackages[] = $formattedPackage;
    }
    
    apiResponse(true, [
        'packages' => $formattedPackages,
        'search_params' => [
            'query' => $query,
            'min_price' => $minPrice,
            'max_price' => $maxPrice,
            'max_duration' => $maxDuration
        ],
        'pagination' => [
            'total' => count($formattedPackages),
            'limit' => $limit,
            'offset' => $offset,
            'has_more' => count($formattedPackages) >= $limit
        ]
    ], 'Search completed successfully');
}

/**
 * Handle getting single package
 */
function handleGetPackage($packageId) {
    global $database;
    
    if (empty($packageId)) {
        apiError('Package ID is required', 400, 'MISSING_PACKAGE_ID');
    }
    
    // Get package with calculated fields
    $package = $database->fetch("
        SELECT
            p.id,
            p.name,
            p.description,
            p.price,
            p.image,
            p.package_duration,
            p.is_active,
            p.created_at,
            p.updated_at,
            COALESCE(SUM(s.price), 0) as original_price,
            COALESCE(SUM(s.duration), 0) as calculated_duration,
            COUNT(ps.service_id) as service_count
        FROM packages p
        LEFT JOIN package_services ps ON p.id = ps.package_id
        LEFT JOIN services s ON ps.service_id = s.id AND s.is_active = 1
        WHERE p.id = ? AND p.is_active = 1
        GROUP BY p.id, p.name, p.description, p.price, p.image, p.package_duration, p.is_active, p.created_at, p.updated_at
    ", [$packageId]);
    
    if (!$package) {
        apiError('Package not found', 404, 'PACKAGE_NOT_FOUND');
    }
    
    // Get package services
    $services = $database->fetchAll("
        SELECT s.id, s.name, s.description, s.price, s.duration, s.category, s.image
        FROM package_services ps
        JOIN services s ON ps.service_id = s.id
        WHERE ps.package_id = ? AND s.is_active = 1
        ORDER BY s.name
    ", [$packageId]);
    
    // Format services for API
    $formattedServices = array_map('formatServiceForApi', $services);
    
    // Format package for API
    $formattedPackage = formatPackageForApi($package);
    
    // Calculate additional fields
    $originalPrice = (float)$package['original_price'];
    $packagePrice = (float)$package['price'];
    
    if ($originalPrice > 0 && $originalPrice > $packagePrice) {
        $formattedPackage['savings'] = $originalPrice - $packagePrice;
        $formattedPackage['discount_percentage'] = round((($originalPrice - $packagePrice) / $originalPrice) * 100);
    } else {
        $formattedPackage['savings'] = 0;
        $formattedPackage['discount_percentage'] = 0;
    }
    
    $formattedPackage['duration'] = $package['package_duration'] > 0 ? 
        (int)$package['package_duration'] : (int)$package['calculated_duration'];
    $formattedPackage['services'] = $formattedServices;
    $formattedPackage['validity_days'] = 30; // Default validity
    
    apiResponse(true, [
        'package' => $formattedPackage
    ], 'Package retrieved successfully');
}
?>
