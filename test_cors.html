<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CORS Test for Flix API</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 5px;
        }
        .success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .info {
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 5px;
            border-radius: 5px;
            cursor: pointer;
        }
        button:hover {
            background-color: #0056b3;
        }
        pre {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 5px;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <h1>CORS Test for Flix Salon & SPA API</h1>
    <p>This page tests if CORS is properly configured for the Flutter Web app to access the API.</p>

    <div id="results"></div>

    <h2>Test Controls</h2>
    <button onclick="testApiRoot()">Test API Root</button>
    <button onclick="testAuthLogin()">Test Auth Login</button>
    <button onclick="testServices()">Test Services</button>
    <button onclick="testOptionsRequest()">Test OPTIONS Request</button>
    <button onclick="clearResults()">Clear Results</button>

    <script>
        const API_BASE = 'http://localhost/flix/api2';
        const resultsDiv = document.getElementById('results');

        function addResult(title, success, message, details = null) {
            const div = document.createElement('div');
            div.className = `test-result ${success ? 'success' : 'error'}`;
            
            let html = `<h3>${title}</h3><p>${message}</p>`;
            if (details) {
                html += `<pre>${JSON.stringify(details, null, 2)}</pre>`;
            }
            
            div.innerHTML = html;
            resultsDiv.appendChild(div);
        }

        function addInfo(title, message) {
            const div = document.createElement('div');
            div.className = 'test-result info';
            div.innerHTML = `<h3>${title}</h3><p>${message}</p>`;
            resultsDiv.appendChild(div);
        }

        async function testApiRoot() {
            addInfo('Testing API Root', 'Making GET request to /api2/');
            
            try {
                const response = await fetch(`${API_BASE}/`, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json'
                    }
                });

                const data = await response.json();
                
                if (response.ok) {
                    addResult('API Root Test', true, `Success! HTTP ${response.status}`, data);
                } else {
                    addResult('API Root Test', false, `Failed with HTTP ${response.status}`, data);
                }
            } catch (error) {
                addResult('API Root Test', false, `CORS Error: ${error.message}`, { error: error.toString() });
            }
        }

        async function testAuthLogin() {
            addInfo('Testing Auth Login', 'Making POST request to /api2/auth/login');
            
            try {
                const response = await fetch(`${API_BASE}/auth/login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json'
                    },
                    body: JSON.stringify({
                        email: '<EMAIL>',
                        password: 'testpassword'
                    })
                });

                const data = await response.json();
                
                if (response.status === 401 || response.status === 400) {
                    addResult('Auth Login Test', true, `CORS working! Got expected HTTP ${response.status} (invalid credentials)`, data);
                } else if (response.ok) {
                    addResult('Auth Login Test', true, `Success! HTTP ${response.status}`, data);
                } else {
                    addResult('Auth Login Test', false, `Unexpected HTTP ${response.status}`, data);
                }
            } catch (error) {
                addResult('Auth Login Test', false, `CORS Error: ${error.message}`, { error: error.toString() });
            }
        }

        async function testServices() {
            addInfo('Testing Services', 'Making GET request to /api2/services/');
            
            try {
                const response = await fetch(`${API_BASE}/services/`, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json'
                    }
                });

                const data = await response.json();
                
                if (response.ok) {
                    addResult('Services Test', true, `Success! HTTP ${response.status}`, { 
                        message: data.message,
                        serviceCount: data.data?.services?.length || 0
                    });
                } else {
                    addResult('Services Test', false, `Failed with HTTP ${response.status}`, data);
                }
            } catch (error) {
                addResult('Services Test', false, `CORS Error: ${error.message}`, { error: error.toString() });
            }
        }

        async function testOptionsRequest() {
            addInfo('Testing OPTIONS Request', 'Making OPTIONS preflight request');
            
            try {
                const response = await fetch(`${API_BASE}/auth/login`, {
                    method: 'OPTIONS',
                    headers: {
                        'Access-Control-Request-Method': 'POST',
                        'Access-Control-Request-Headers': 'Content-Type'
                    }
                });

                const headers = {};
                for (let [key, value] of response.headers.entries()) {
                    if (key.startsWith('access-control-')) {
                        headers[key] = value;
                    }
                }
                
                if (response.ok) {
                    addResult('OPTIONS Test', true, `Success! HTTP ${response.status}`, { corsHeaders: headers });
                } else {
                    addResult('OPTIONS Test', false, `Failed with HTTP ${response.status}`, { corsHeaders: headers });
                }
            } catch (error) {
                addResult('OPTIONS Test', false, `CORS Error: ${error.message}`, { error: error.toString() });
            }
        }

        function clearResults() {
            resultsDiv.innerHTML = '';
        }

        // Auto-run basic test on page load
        window.addEventListener('load', () => {
            addInfo('CORS Test Started', 'Testing CORS configuration for Flutter Web compatibility...');
            setTimeout(testApiRoot, 1000);
        });
    </script>
</body>
</html>
