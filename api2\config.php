<?php
/**
 * API2 Configuration
 * Mobile API for Flutter Application
 * Flix Salon & SPA - PHP Version
 */

// Include main app configuration
require_once __DIR__ . '/../config/app.php';

// API Configuration
define('API_VERSION', '2.0');
define('API_BASE_PATH', '/api2');

/**
 * Set JSON headers and CORS for mobile app
 */
function setApiHeaders() {
    header('Content-Type: application/json; charset=utf-8');
    header('Access-Control-Allow-Origin: *');
    header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
    header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');
    
    // Handle preflight requests
    if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
        http_response_code(200);
        exit();
    }
}

/**
 * Detect if request is from mobile API
 */
function isApiRequest() {
    $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? '';
    $requestUri = $_SERVER['REQUEST_URI'] ?? '';
    
    return strpos($requestUri, '/api2/') !== false || 
           strpos($userAgent, 'Flutter') !== false ||
           strpos($userAgent, 'Dart') !== false ||
           isset($_SERVER['HTTP_X_REQUESTED_WITH']);
}

/**
 * Standard API response format
 */
function apiResponse($success = true, $data = null, $message = '', $statusCode = 200) {
    http_response_code($statusCode);
    
    $response = [
        'success' => $success,
        'timestamp' => date('c'),
        'api_version' => API_VERSION
    ];
    
    if ($message) {
        $response['message'] = $message;
    }
    
    if ($data !== null) {
        $response['data'] = $data;
    }
    
    if (!$success && !$message) {
        $response['message'] = 'An error occurred';
    }
    
    echo json_encode($response, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
    exit();
}

/**
 * API error response
 */
function apiError($message, $statusCode = 400, $errorCode = null) {
    $response = [
        'success' => false,
        'message' => $message,
        'timestamp' => date('c'),
        'api_version' => API_VERSION
    ];
    
    if ($errorCode) {
        $response['error_code'] = $errorCode;
    }
    
    http_response_code($statusCode);
    echo json_encode($response, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
    exit();
}

/**
 * Validate required parameters
 */
function validateRequired($data, $required) {
    $missing = [];
    foreach ($required as $field) {
        if (!isset($data[$field]) || empty($data[$field])) {
            $missing[] = $field;
        }
    }
    
    if (!empty($missing)) {
        apiError('Missing required fields: ' . implode(', ', $missing), 400, 'MISSING_FIELDS');
    }
}

/**
 * Get request data (supports both JSON and form data)
 */
function getRequestData() {
    $contentType = $_SERVER['CONTENT_TYPE'] ?? '';
    
    if (strpos($contentType, 'application/json') !== false) {
        $input = file_get_contents('php://input');
        $data = json_decode($input, true);
        
        if (json_last_error() !== JSON_ERROR_NONE) {
            apiError('Invalid JSON format', 400, 'INVALID_JSON');
        }
        
        return $data ?: [];
    }
    
    return $_POST;
}

/**
 * Authenticate API request
 */
function authenticateApiRequest($requireAuth = true) {
    // Check for session-based authentication
    if (isLoggedIn()) {
        return [
            'authenticated' => true,
            'user_id' => $_SESSION['user_id'],
            'user_role' => $_SESSION['user_role'],
            'user_name' => $_SESSION['user_name']
        ];
    }
    
    // Check for Authorization header (for future token-based auth)
    $headers = getallheaders();
    $authHeader = $headers['Authorization'] ?? $headers['authorization'] ?? '';
    
    if ($authHeader && strpos($authHeader, 'Bearer ') === 0) {
        $token = substr($authHeader, 7);
        // TODO: Implement token validation when needed
        // For now, we'll use session-based auth
    }
    
    if ($requireAuth) {
        apiError('Authentication required', 401, 'AUTH_REQUIRED');
    }
    
    return ['authenticated' => false];
}

/**
 * Check if user has specific role
 */
function requireRole($role) {
    $auth = authenticateApiRequest(true);
    
    if ($auth['user_role'] !== $role) {
        apiError('Insufficient permissions', 403, 'INSUFFICIENT_PERMISSIONS');
    }
    
    return $auth;
}

/**
 * Format image URL for API response
 */
function formatImageUrl($imagePath) {
    if (!$imagePath) {
        return null;
    }
    
    if (filter_var($imagePath, FILTER_VALIDATE_URL)) {
        return $imagePath;
    }
    
    return getBasePath() . '/uploads/' . $imagePath;
}

/**
 * Format service/package data for API response
 */
function formatServiceForApi($service) {
    $formatted = [
        'id' => $service['id'],
        'name' => $service['name'],
        'description' => $service['description'] ?? '',
        'duration' => (int)$service['duration'],
        'category' => $service['category'] ?? '',
        'image' => formatImageUrl($service['image']),
        'is_active' => (bool)$service['is_active']
    ];
    
    // Handle pricing based on user role
    if (shouldShowPricing()) {
        $formatted['price'] = (float)$service['price'];
        $formatted['show_pricing'] = true;
    } else {
        $formatted['price'] = 0;
        $formatted['show_pricing'] = false;
        $formatted['price_text'] = 'Contact for pricing';
    }
    
    return $formatted;
}

/**
 * Format package data for API response
 */
function formatPackageForApi($package) {
    $formatted = [
        'id' => $package['id'],
        'name' => $package['name'],
        'description' => $package['description'] ?? '',
        'duration' => (int)($package['package_duration'] ?? $package['duration'] ?? 0),
        'image' => formatImageUrl($package['image']),
        'is_active' => (bool)$package['is_active'],
        'service_count' => (int)($package['service_count'] ?? 0)
    ];
    
    // Handle pricing based on user role
    if (shouldShowPricing()) {
        $formatted['price'] = (float)$package['price'];
        $formatted['original_price'] = (float)($package['original_price'] ?? 0);
        $formatted['savings'] = (float)($package['savings'] ?? 0);
        $formatted['discount_percentage'] = (int)($package['discount_percentage'] ?? 0);
        $formatted['show_pricing'] = true;
    } else {
        $formatted['price'] = 0;
        $formatted['original_price'] = 0;
        $formatted['savings'] = 0;
        $formatted['discount_percentage'] = 0;
        $formatted['show_pricing'] = false;
        $formatted['price_text'] = 'Contact for pricing';
    }
    
    return $formatted;
}

/**
 * Format booking data for API response
 */
function formatBookingForApi($booking) {
    return [
        'id' => $booking['id'],
        'service_name' => $booking['service_name'] ?? '',
        'package_name' => $booking['package_name'] ?? '',
        'staff_name' => $booking['staff_name'] ?? '',
        'date' => $booking['date'],
        'start_time' => $booking['start_time'],
        'end_time' => $booking['end_time'],
        'total_amount' => (float)$booking['total_amount'],
        'points_used' => (int)$booking['points_used'],
        'points_earned' => (int)$booking['points_earned'],
        'status' => $booking['status'],
        'notes' => $booking['notes'] ?? '',
        'created_at' => $booking['created_at'],
        'payment_status' => $booking['payment_status'] ?? null,
        'payment_gateway' => $booking['payment_gateway'] ?? null
    ];
}

/**
 * Log API request for debugging
 */
function logApiRequest($endpoint, $method, $data = null) {
    if (!isProduction()) {
        $logData = [
            'timestamp' => date('c'),
            'endpoint' => $endpoint,
            'method' => $method,
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? '',
            'ip' => $_SERVER['REMOTE_ADDR'] ?? '',
            'data' => $data
        ];
        
        error_log('API2 Request: ' . json_encode($logData));
    }
}

// Initialize API
setApiHeaders();
?>
