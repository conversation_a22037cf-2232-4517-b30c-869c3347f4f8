<?php
/**
 * Services API Endpoints
 * Mobile API for Flutter Application
 * Flix Salon & SPA - PHP Version
 */

require_once __DIR__ . '/../config.php';

// Parse the request path
$requestUri = $_SERVER['REQUEST_URI'];
$basePath = getBasePath() . '/api2/services';

$path = parse_url($requestUri, PHP_URL_PATH);
if (strpos($path, $basePath) === 0) {
    $path = substr($path, strlen($basePath));
}

$path = ltrim($path, '/');
$segments = array_filter(explode('/', $path));

$method = $_SERVER['REQUEST_METHOD'];
$action = $segments[0] ?? '';

try {
    switch ($action) {
        case '':
            // GET /services - List all services
            if ($method !== 'GET') {
                apiError('Method not allowed', 405, 'METHOD_NOT_ALLOWED');
            }
            handleListServices();
            break;
            
        case 'categories':
            // GET /services/categories - Get service categories
            if ($method !== 'GET') {
                apiError('Method not allowed', 405, 'METHOD_NOT_ALLOWED');
            }
            handleGetCategories();
            break;
            
        case 'search':
            // GET /services/search - Search services
            if ($method !== 'GET') {
                apiError('Method not allowed', 405, 'METHOD_NOT_ALLOWED');
            }
            handleSearchServices();
            break;
            
        default:
            // GET /services/{id} - Get service details
            if ($method !== 'GET') {
                apiError('Method not allowed', 405, 'METHOD_NOT_ALLOWED');
            }
            handleGetService($action);
            break;
    }
    
} catch (Exception $e) {
    error_log('Services API Error: ' . $e->getMessage());
    apiError('Services error', 500, 'SERVICES_ERROR');
}

/**
 * Handle listing all services
 */
function handleListServices() {
    global $database;
    
    // Get query parameters
    $category = $_GET['category'] ?? '';
    $limit = min((int)($_GET['limit'] ?? 50), 100); // Max 100 items
    $offset = max((int)($_GET['offset'] ?? 0), 0);
    $sortBy = $_GET['sort_by'] ?? 'name';
    $sortOrder = strtoupper($_GET['sort_order'] ?? 'ASC') === 'DESC' ? 'DESC' : 'ASC';
    
    // Validate sort field
    $allowedSortFields = ['name', 'price', 'duration', 'category', 'created_at'];
    if (!in_array($sortBy, $allowedSortFields)) {
        $sortBy = 'name';
    }
    
    // Build query
    $whereClause = "WHERE is_active = 1";
    $params = [];
    
    if ($category) {
        $whereClause .= " AND category = ?";
        $params[] = $category;
    }
    
    // Get total count
    $totalCount = $database->fetch(
        "SELECT COUNT(*) as count FROM services $whereClause",
        $params
    )['count'];
    
    // Get services
    $services = $database->fetchAll(
        "SELECT * FROM services $whereClause ORDER BY $sortBy $sortOrder LIMIT $limit OFFSET $offset",
        $params
    );
    
    // Format services for API
    $formattedServices = array_map('formatServiceForApi', $services);
    
    apiResponse(true, [
        'services' => $formattedServices,
        'pagination' => [
            'total' => (int)$totalCount,
            'limit' => $limit,
            'offset' => $offset,
            'has_more' => ($offset + $limit) < $totalCount
        ]
    ], 'Services retrieved successfully');
}

/**
 * Handle getting service categories
 */
function handleGetCategories() {
    global $database;
    
    $categories = $database->fetchAll(
        "SELECT DISTINCT category as name, COUNT(*) as service_count 
         FROM services 
         WHERE is_active = 1 AND category IS NOT NULL AND category != ''
         GROUP BY category 
         ORDER BY category"
    );
    
    // Also get subcategories if they exist
    $subcategories = $database->fetchAll(
        "SELECT sc.id, sc.name, sc.category_id, c.name as category_name
         FROM subcategories sc
         LEFT JOIN service_categories c ON sc.category_id = c.id
         WHERE sc.is_active = 1
         ORDER BY c.name, sc.name"
    );
    
    apiResponse(true, [
        'categories' => $categories,
        'subcategories' => $subcategories
    ], 'Categories retrieved successfully');
}

/**
 * Handle searching services
 */
function handleSearchServices() {
    global $database;
    
    $query = $_GET['q'] ?? '';
    $category = $_GET['category'] ?? '';
    $minPrice = $_GET['min_price'] ?? '';
    $maxPrice = $_GET['max_price'] ?? '';
    $maxDuration = $_GET['max_duration'] ?? '';
    $limit = min((int)($_GET['limit'] ?? 20), 50);
    $offset = max((int)($_GET['offset'] ?? 0), 0);
    
    if (empty($query) && empty($category) && empty($minPrice) && empty($maxPrice) && empty($maxDuration)) {
        apiError('At least one search parameter is required', 400, 'MISSING_SEARCH_PARAMS');
    }
    
    $whereClause = "WHERE is_active = 1";
    $params = [];
    
    if ($query) {
        $whereClause .= " AND (name LIKE ? OR description LIKE ?)";
        $params[] = "%$query%";
        $params[] = "%$query%";
    }
    
    if ($category) {
        $whereClause .= " AND category = ?";
        $params[] = $category;
    }
    
    if ($minPrice !== '' && shouldShowPricing()) {
        $whereClause .= " AND price >= ?";
        $params[] = floatval($minPrice);
    }
    
    if ($maxPrice !== '' && shouldShowPricing()) {
        $whereClause .= " AND price <= ?";
        $params[] = floatval($maxPrice);
    }
    
    if ($maxDuration !== '') {
        $whereClause .= " AND duration <= ?";
        $params[] = intval($maxDuration);
    }
    
    // Get total count
    $totalCount = $database->fetch(
        "SELECT COUNT(*) as count FROM services $whereClause",
        $params
    )['count'];
    
    // Get services
    $services = $database->fetchAll(
        "SELECT * FROM services $whereClause ORDER BY name ASC LIMIT $limit OFFSET $offset",
        $params
    );
    
    // Format services for API
    $formattedServices = array_map('formatServiceForApi', $services);
    
    apiResponse(true, [
        'services' => $formattedServices,
        'search_params' => [
            'query' => $query,
            'category' => $category,
            'min_price' => $minPrice,
            'max_price' => $maxPrice,
            'max_duration' => $maxDuration
        ],
        'pagination' => [
            'total' => (int)$totalCount,
            'limit' => $limit,
            'offset' => $offset,
            'has_more' => ($offset + $limit) < $totalCount
        ]
    ], 'Search completed successfully');
}

/**
 * Handle getting single service
 */
function handleGetService($serviceId) {
    global $database;
    
    if (empty($serviceId)) {
        apiError('Service ID is required', 400, 'MISSING_SERVICE_ID');
    }
    
    $service = $database->fetch(
        "SELECT * FROM services WHERE id = ? AND is_active = 1",
        [$serviceId]
    );
    
    if (!$service) {
        apiError('Service not found', 404, 'SERVICE_NOT_FOUND');
    }
    
    // Get service variations if any
    $variations = $database->fetchAll(
        "SELECT * FROM service_variations WHERE service_id = ? AND is_active = 1 ORDER BY name",
        [$serviceId]
    );
    
    // Format variations for API
    $formattedVariations = [];
    foreach ($variations as $variation) {
        $formattedVariation = [
            'id' => $variation['id'],
            'name' => $variation['name'],
            'description' => $variation['description'] ?? '',
            'duration' => (int)$variation['duration']
        ];
        
        if (shouldShowPricing()) {
            $formattedVariation['price'] = (float)$variation['price'];
        } else {
            $formattedVariation['price'] = 0;
            $formattedVariation['price_text'] = 'Contact for pricing';
        }
        
        $formattedVariations[] = $formattedVariation;
    }
    
    // Format service for API
    $formattedService = formatServiceForApi($service);
    $formattedService['variations'] = $formattedVariations;
    $formattedService['has_variations'] = !empty($variations);
    
    apiResponse(true, [
        'service' => $formattedService
    ], 'Service retrieved successfully');
}
?>
