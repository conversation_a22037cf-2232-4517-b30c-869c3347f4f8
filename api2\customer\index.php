<?php
/**
 * Customer Panel API Endpoints
 * Mobile API for Flutter Application
 * Flix Salon & SPA - PHP Version
 */

require_once __DIR__ . '/../config.php';
require_once __DIR__ . '/../../includes/customer_panel_functions.php';

// Parse the request path
$requestUri = $_SERVER['REQUEST_URI'];
$basePath = getBasePath() . '/api2/customer';

$path = parse_url($requestUri, PHP_URL_PATH);
if (strpos($path, $basePath) === 0) {
    $path = substr($path, strlen($basePath));
}

$path = ltrim($path, '/');
$segments = array_filter(explode('/', $path));

$method = $_SERVER['REQUEST_METHOD'];
$action = $segments[0] ?? '';

// Require customer authentication for all endpoints
$auth = authenticateApiRequest(true);
if ($auth['user_role'] !== 'CUSTOMER') {
    apiError('Customer access required', 403, 'CUSTOMER_ACCESS_REQUIRED');
}

$customerId = $auth['user_id'];

try {
    switch ($action) {
        case 'dashboard':
            if ($method !== 'GET') {
                apiError('Method not allowed', 405, 'METHOD_NOT_ALLOWED');
            }
            handleGetDashboard($customerId);
            break;
            
        case 'profile':
            if ($method === 'GET') {
                handleGetProfile($customerId);
            } elseif ($method === 'PUT') {
                handleUpdateProfile($customerId);
            } else {
                apiError('Method not allowed', 405, 'METHOD_NOT_ALLOWED');
            }
            break;
            
        case 'bookings':
            if ($method !== 'GET') {
                apiError('Method not allowed', 405, 'METHOD_NOT_ALLOWED');
            }
            handleGetBookings($customerId);
            break;
            
        case 'points':
            if ($method !== 'GET') {
                apiError('Method not allowed', 405, 'METHOD_NOT_ALLOWED');
            }
            handleGetPoints($customerId);
            break;
            
        case 'stats':
            if ($method !== 'GET') {
                apiError('Method not allowed', 405, 'METHOD_NOT_ALLOWED');
            }
            handleGetStats($customerId);
            break;
            
        default:
            apiError('Customer endpoint not found', 404, 'ENDPOINT_NOT_FOUND');
            break;
    }
    
} catch (Exception $e) {
    error_log('Customer API Error: ' . $e->getMessage());
    apiError('Customer panel error', 500, 'CUSTOMER_ERROR');
}

/**
 * Handle getting customer dashboard data
 */
function handleGetDashboard($customerId) {
    try {
        $dashboardData = getCustomerDashboardData($customerId);
        
        // Format data for API
        $formattedData = [
            'profile' => [
                'id' => $dashboardData['profile']['id'],
                'name' => $dashboardData['profile']['name'],
                'email' => $dashboardData['profile']['email'],
                'phone' => $dashboardData['profile']['phone'] ?? '',
                'points' => (int)$dashboardData['profile']['points'],
                'avatar' => formatImageUrl($dashboardData['profile']['avatar']),
                'member_since' => $dashboardData['profile']['created_at']
            ],
            'upcoming_bookings' => array_map('formatBookingForApi', $dashboardData['upcomingBookings']),
            'recent_bookings' => array_map('formatBookingForApi', $dashboardData['recentBookings']),
            'points_data' => [
                'current_points' => (int)$dashboardData['pointsData']['currentPoints'],
                'total_earned' => (int)$dashboardData['pointsData']['totalEarned'],
                'total_redeemed' => (int)$dashboardData['pointsData']['totalRedeemed'],
                'recent_transactions' => $dashboardData['pointsData']['recentTransactions']
            ],
            'loyalty_tier' => [
                'name' => $dashboardData['loyaltyTier']['name'],
                'level' => $dashboardData['loyaltyTier']['level'],
                'points_multiplier' => (float)$dashboardData['loyaltyTier']['pointsMultiplier'],
                'next_tier' => $dashboardData['loyaltyTier']['nextTier'] ?? null,
                'points_to_next' => (int)($dashboardData['loyaltyTier']['pointsToNext'] ?? 0)
            ],
            'stats' => [
                'total_bookings' => (int)$dashboardData['stats']['totalBookings'],
                'completed_bookings' => (int)$dashboardData['stats']['completedBookings'],
                'total_spent' => (float)$dashboardData['stats']['totalSpent'],
                'favorite_service' => $dashboardData['stats']['favoriteService'] ?? null,
                'last_visit' => $dashboardData['stats']['lastVisit'] ?? null
            ]
        ];
        
        apiResponse(true, $formattedData, 'Dashboard data retrieved successfully');
        
    } catch (Exception $e) {
        error_log('Dashboard error: ' . $e->getMessage());
        apiError('Failed to load dashboard data', 500, 'DASHBOARD_ERROR');
    }
}

/**
 * Handle getting customer profile
 */
function handleGetProfile($customerId) {
    try {
        $profile = getCustomerProfile($customerId);
        
        $formattedProfile = [
            'id' => $profile['id'],
            'name' => $profile['name'],
            'email' => $profile['email'],
            'phone' => $profile['phone'] ?? '',
            'date_of_birth' => $profile['date_of_birth'] ?? null,
            'points' => (int)$profile['points'],
            'referral_code' => $profile['referral_code'],
            'avatar' => formatImageUrl($profile['avatar']),
            'member_since' => $profile['created_at']
        ];
        
        apiResponse(true, ['profile' => $formattedProfile], 'Profile retrieved successfully');
        
    } catch (Exception $e) {
        error_log('Profile error: ' . $e->getMessage());
        apiError('Failed to load profile', 500, 'PROFILE_ERROR');
    }
}

/**
 * Handle updating customer profile
 */
function handleUpdateProfile($customerId) {
    try {
        $data = getRequestData();
        
        // Validate allowed fields
        $allowedFields = ['name', 'phone', 'date_of_birth', 'image'];
        $updateData = [];
        
        foreach ($allowedFields as $field) {
            if (isset($data[$field])) {
                $updateData[$field] = $data[$field];
            }
        }
        
        if (empty($updateData)) {
            apiError('No valid fields to update', 400, 'NO_UPDATE_FIELDS');
        }
        
        // Validate phone number if provided
        if (isset($updateData['phone']) && !empty($updateData['phone'])) {
            if (!preg_match('/^[+]?[0-9\s\-\(\)]{10,15}$/', $updateData['phone'])) {
                apiError('Invalid phone number format', 400, 'INVALID_PHONE');
            }
        }
        
        // Validate date of birth if provided
        if (isset($updateData['date_of_birth']) && !empty($updateData['date_of_birth'])) {
            $date = DateTime::createFromFormat('Y-m-d', $updateData['date_of_birth']);
            if (!$date || $date->format('Y-m-d') !== $updateData['date_of_birth']) {
                apiError('Invalid date format. Use YYYY-MM-DD', 400, 'INVALID_DATE');
            }
            
            // Check if date is not in the future
            if ($date > new DateTime()) {
                apiError('Date of birth cannot be in the future', 400, 'FUTURE_DATE');
            }
        }
        
        $updatedProfile = updateCustomerProfile($customerId, $updateData);
        
        $formattedProfile = [
            'id' => $updatedProfile['id'],
            'name' => $updatedProfile['name'],
            'email' => $updatedProfile['email'],
            'phone' => $updatedProfile['phone'] ?? '',
            'date_of_birth' => $updatedProfile['date_of_birth'] ?? null,
            'points' => (int)$updatedProfile['points'],
            'referral_code' => $updatedProfile['referral_code'],
            'avatar' => formatImageUrl($updatedProfile['avatar']),
            'member_since' => $updatedProfile['created_at']
        ];
        
        apiResponse(true, ['profile' => $formattedProfile], 'Profile updated successfully');
        
    } catch (Exception $e) {
        error_log('Profile update error: ' . $e->getMessage());
        apiError($e->getMessage(), 400, 'PROFILE_UPDATE_ERROR');
    }
}

/**
 * Handle getting customer bookings
 */
function handleGetBookings($customerId) {
    global $database;
    
    try {
        $status = $_GET['status'] ?? '';
        $limit = min((int)($_GET['limit'] ?? 20), 50);
        $offset = max((int)($_GET['offset'] ?? 0), 0);
        
        $whereClause = "WHERE b.user_id = ?";
        $params = [$customerId];
        
        if ($status) {
            $allowedStatuses = ['PENDING', 'CONFIRMED', 'IN_PROGRESS', 'COMPLETED', 'CANCELLED', 'NO_SHOW', 'EXPIRED'];
            if (in_array(strtoupper($status), $allowedStatuses)) {
                $whereClause .= " AND b.status = ?";
                $params[] = strtoupper($status);
            }
        }
        
        // Get total count
        $totalCount = $database->fetch(
            "SELECT COUNT(*) as count FROM bookings b $whereClause",
            $params
        )['count'];
        
        // Get bookings
        $bookings = $database->fetchAll("
            SELECT
                b.*,
                s.name as service_name,
                s.duration as service_duration,
                sv.name as variation_name,
                p.name as package_name,
                st.name as staff_name,
                st.phone as staff_phone,
                pay.status as payment_status,
                pay.payment_gateway
            FROM bookings b
            LEFT JOIN services s ON b.service_id = s.id
            LEFT JOIN service_variations sv ON b.service_variation_id = sv.id
            LEFT JOIN packages p ON b.package_id = p.id
            LEFT JOIN users st ON b.staff_id = st.id AND st.role = 'STAFF'
            LEFT JOIN payments pay ON b.id = pay.booking_id
            $whereClause
            ORDER BY b.date DESC, b.start_time DESC
            LIMIT $limit OFFSET $offset
        ", $params);
        
        $formattedBookings = array_map('formatBookingForApi', $bookings);
        
        apiResponse(true, [
            'bookings' => $formattedBookings,
            'pagination' => [
                'total' => (int)$totalCount,
                'limit' => $limit,
                'offset' => $offset,
                'has_more' => ($offset + $limit) < $totalCount
            ]
        ], 'Bookings retrieved successfully');
        
    } catch (Exception $e) {
        error_log('Bookings error: ' . $e->getMessage());
        apiError('Failed to load bookings', 500, 'BOOKINGS_ERROR');
    }
}

/**
 * Handle getting customer points data
 */
function handleGetPoints($customerId) {
    try {
        $pointsData = getCustomerPointsData($customerId);
        $loyaltyTier = getCustomerLoyaltyTierEnhanced($customerId);
        
        $formattedData = [
            'current_points' => (int)$pointsData['currentPoints'],
            'total_earned' => (int)$pointsData['totalEarned'],
            'total_redeemed' => (int)$pointsData['totalRedeemed'],
            'recent_transactions' => $pointsData['recentTransactions'],
            'loyalty_tier' => [
                'name' => $loyaltyTier['name'],
                'level' => $loyaltyTier['level'],
                'points_multiplier' => (float)$loyaltyTier['pointsMultiplier'],
                'next_tier' => $loyaltyTier['nextTier'] ?? null,
                'points_to_next' => (int)($loyaltyTier['pointsToNext'] ?? 0)
            ]
        ];
        
        apiResponse(true, $formattedData, 'Points data retrieved successfully');
        
    } catch (Exception $e) {
        error_log('Points error: ' . $e->getMessage());
        apiError('Failed to load points data', 500, 'POINTS_ERROR');
    }
}

/**
 * Handle getting customer statistics
 */
function handleGetStats($customerId) {
    try {
        $stats = getIndividualCustomerStats($customerId);
        
        $formattedStats = [
            'total_bookings' => (int)$stats['totalBookings'],
            'completed_bookings' => (int)$stats['completedBookings'],
            'cancelled_bookings' => (int)$stats['cancelledBookings'],
            'total_spent' => (float)$stats['totalSpent'],
            'average_booking_value' => (float)$stats['averageBookingValue'],
            'favorite_service' => $stats['favoriteService'] ?? null,
            'favorite_staff' => $stats['favoriteStaff'] ?? null,
            'last_visit' => $stats['lastVisit'] ?? null,
            'next_booking' => $stats['nextBooking'] ?? null,
            'member_since' => $stats['memberSince']
        ];
        
        apiResponse(true, $formattedStats, 'Statistics retrieved successfully');
        
    } catch (Exception $e) {
        error_log('Stats error: ' . $e->getMessage());
        apiError('Failed to load statistics', 500, 'STATS_ERROR');
    }
}
?>
