<?php
/**
 * Rewards API Endpoints
 * Mobile API for Flutter Application
 * Flix Salon & SPA - PHP Version
 */

require_once __DIR__ . '/../config.php';
require_once __DIR__ . '/../../includes/customer_panel_functions.php';

// Parse the request path
$requestUri = $_SERVER['REQUEST_URI'];
$basePath = getBasePath() . '/api2/rewards';

$path = parse_url($requestUri, PHP_URL_PATH);
if (strpos($path, $basePath) === 0) {
    $path = substr($path, strlen($basePath));
}

$path = ltrim($path, '/');
$segments = array_filter(explode('/', $path));

$method = $_SERVER['REQUEST_METHOD'];
$action = $segments[0] ?? '';

// Require customer authentication for all endpoints
$auth = authenticateApiRequest(true);
if ($auth['user_role'] !== 'CUSTOMER') {
    apiError('Customer access required', 403, 'CUSTOMER_ACCESS_REQUIRED');
}

$customerId = $auth['user_id'];

try {
    switch ($action) {
        case '':
            // GET /rewards - Get rewards overview
            if ($method !== 'GET') {
                apiError('Method not allowed', 405, 'METHOD_NOT_ALLOWED');
            }
            handleGetRewardsOverview($customerId);
            break;
            
        case 'points':
            // GET /rewards/points - Get points details
            if ($method !== 'GET') {
                apiError('Method not allowed', 405, 'METHOD_NOT_ALLOWED');
            }
            handleGetPointsDetails($customerId);
            break;
            
        case 'transactions':
            // GET /rewards/transactions - Get points transactions
            if ($method !== 'GET') {
                apiError('Method not allowed', 405, 'METHOD_NOT_ALLOWED');
            }
            handleGetPointsTransactions($customerId);
            break;
            
        case 'redeem':
            // POST /rewards/redeem - Redeem points
            if ($method !== 'POST') {
                apiError('Method not allowed', 405, 'METHOD_NOT_ALLOWED');
            }
            handleRedeemPoints($customerId);
            break;
            
        case 'loyalty':
            // GET /rewards/loyalty - Get loyalty tier info
            if ($method !== 'GET') {
                apiError('Method not allowed', 405, 'METHOD_NOT_ALLOWED');
            }
            handleGetLoyaltyInfo($customerId);
            break;
            
        case 'referral':
            // GET /rewards/referral - Get referral info
            if ($method !== 'GET') {
                apiError('Method not allowed', 405, 'METHOD_NOT_ALLOWED');
            }
            handleGetReferralInfo($customerId);
            break;
            
        default:
            apiError('Rewards endpoint not found', 404, 'ENDPOINT_NOT_FOUND');
            break;
    }
    
} catch (Exception $e) {
    error_log('Rewards API Error: ' . $e->getMessage());
    apiError('Rewards error', 500, 'REWARDS_ERROR');
}

/**
 * Handle getting rewards overview
 */
function handleGetRewardsOverview($customerId) {
    try {
        $pointsData = getCustomerPointsData($customerId);
        $loyaltyTier = getCustomerLoyaltyTierEnhanced($customerId);
        $referralData = getCustomerReferralData($customerId);
        
        $overview = [
            'points' => [
                'current' => (int)$pointsData['currentPoints'],
                'total_earned' => (int)$pointsData['totalEarned'],
                'total_redeemed' => (int)$pointsData['totalRedeemed'],
                'pending' => (int)($pointsData['pendingPoints'] ?? 0)
            ],
            'loyalty_tier' => [
                'name' => $loyaltyTier['name'],
                'level' => $loyaltyTier['level'],
                'points_multiplier' => (float)$loyaltyTier['pointsMultiplier'],
                'next_tier' => $loyaltyTier['nextTier'] ?? null,
                'points_to_next' => (int)($loyaltyTier['pointsToNext'] ?? 0),
                'benefits' => $loyaltyTier['benefits'] ?? []
            ],
            'referral' => [
                'code' => $referralData['code'],
                'total_referrals' => (int)$referralData['totalReferrals'],
                'points_earned' => (int)$referralData['pointsEarned'],
                'bonus_rate' => (int)($referralData['bonusRate'] ?? 100) // Points per referral
            ],
            'recent_transactions' => array_slice($pointsData['recentTransactions'], 0, 5)
        ];
        
        apiResponse(true, $overview, 'Rewards overview retrieved successfully');
        
    } catch (Exception $e) {
        error_log('Rewards overview error: ' . $e->getMessage());
        apiError('Failed to get rewards overview', 500, 'REWARDS_OVERVIEW_ERROR');
    }
}

/**
 * Handle getting points details
 */
function handleGetPointsDetails($customerId) {
    try {
        $pointsData = getCustomerPointsData($customerId);
        
        // Calculate points breakdown
        global $database;
        
        $breakdown = $database->fetchAll("
            SELECT 
                transaction_type,
                SUM(CASE WHEN points > 0 THEN points ELSE 0 END) as earned,
                SUM(CASE WHEN points < 0 THEN ABS(points) ELSE 0 END) as spent,
                COUNT(*) as transaction_count
            FROM points_transactions 
            WHERE user_id = ? 
            GROUP BY transaction_type
            ORDER BY earned DESC
        ", [$customerId]);
        
        $details = [
            'current_points' => (int)$pointsData['currentPoints'],
            'total_earned' => (int)$pointsData['totalEarned'],
            'total_redeemed' => (int)$pointsData['totalRedeemed'],
            'pending_points' => (int)($pointsData['pendingPoints'] ?? 0),
            'points_value' => [
                'currency' => 'TSH',
                'rate' => 10, // 1 point = 10 TSH
                'current_value' => (int)$pointsData['currentPoints'] * 10
            ],
            'breakdown' => $breakdown,
            'expiry_info' => [
                'points_expire' => true,
                'expiry_period' => '12 months',
                'next_expiry_date' => null, // You can implement this
                'expiring_points' => 0 // You can implement this
            ]
        ];
        
        apiResponse(true, $details, 'Points details retrieved successfully');
        
    } catch (Exception $e) {
        error_log('Points details error: ' . $e->getMessage());
        apiError('Failed to get points details', 500, 'POINTS_DETAILS_ERROR');
    }
}

/**
 * Handle getting points transactions
 */
function handleGetPointsTransactions($customerId) {
    global $database;
    
    try {
        $limit = min((int)($_GET['limit'] ?? 20), 50);
        $offset = max((int)($_GET['offset'] ?? 0), 0);
        $type = $_GET['type'] ?? ''; // 'earned' or 'redeemed'
        
        $whereClause = "WHERE user_id = ?";
        $params = [$customerId];
        
        if ($type === 'earned') {
            $whereClause .= " AND points > 0";
        } elseif ($type === 'redeemed') {
            $whereClause .= " AND points < 0";
        }
        
        // Get total count
        $totalCount = $database->fetch(
            "SELECT COUNT(*) as count FROM points_transactions $whereClause",
            $params
        )['count'];
        
        // Get transactions
        $transactions = $database->fetchAll("
            SELECT 
                id,
                points,
                transaction_type,
                description,
                booking_id,
                created_at
            FROM points_transactions 
            $whereClause
            ORDER BY created_at DESC
            LIMIT $limit OFFSET $offset
        ", $params);
        
        // Format transactions
        $formattedTransactions = [];
        foreach ($transactions as $transaction) {
            $formattedTransactions[] = [
                'id' => $transaction['id'],
                'points' => (int)$transaction['points'],
                'type' => $transaction['transaction_type'],
                'description' => $transaction['description'],
                'booking_id' => $transaction['booking_id'],
                'date' => $transaction['created_at'],
                'is_credit' => $transaction['points'] > 0
            ];
        }
        
        apiResponse(true, [
            'transactions' => $formattedTransactions,
            'pagination' => [
                'total' => (int)$totalCount,
                'limit' => $limit,
                'offset' => $offset,
                'has_more' => ($offset + $limit) < $totalCount
            ]
        ], 'Points transactions retrieved successfully');
        
    } catch (Exception $e) {
        error_log('Points transactions error: ' . $e->getMessage());
        apiError('Failed to get points transactions', 500, 'POINTS_TRANSACTIONS_ERROR');
    }
}

/**
 * Handle redeeming points
 */
function handleRedeemPoints($customerId) {
    try {
        $data = getRequestData();
        
        validateRequired($data, ['points_amount', 'redemption_type']);
        
        $pointsAmount = (int)$data['points_amount'];
        $redemptionType = $data['redemption_type'];
        $bookingId = $data['booking_id'] ?? null;
        
        if ($pointsAmount <= 0) {
            apiError('Points amount must be greater than 0', 400, 'INVALID_POINTS_AMOUNT');
        }
        
        // Check available points
        global $database;
        $user = $database->fetch(
            "SELECT points FROM users WHERE id = ?",
            [$customerId]
        );
        
        if (!$user || $user['points'] < $pointsAmount) {
            apiError('Insufficient points', 400, 'INSUFFICIENT_POINTS');
        }
        
        // Validate redemption type
        $allowedTypes = ['booking_discount', 'cash_voucher', 'service_upgrade'];
        if (!in_array($redemptionType, $allowedTypes)) {
            apiError('Invalid redemption type', 400, 'INVALID_REDEMPTION_TYPE');
        }
        
        // For booking discount, validate booking
        if ($redemptionType === 'booking_discount') {
            if (!$bookingId) {
                apiError('Booking ID is required for booking discount', 400, 'MISSING_BOOKING_ID');
            }
            
            $booking = $database->fetch(
                "SELECT id, status, total_amount FROM bookings WHERE id = ? AND user_id = ?",
                [$bookingId, $customerId]
            );
            
            if (!$booking || $booking['status'] !== 'PENDING') {
                apiError('Invalid booking for points redemption', 400, 'INVALID_BOOKING');
            }
        }
        
        // Process redemption
        $result = redeemCustomerPoints($customerId, $pointsAmount, $redemptionType, $bookingId);
        
        if ($result['success']) {
            // Get updated points balance
            $updatedUser = $database->fetch(
                "SELECT points FROM users WHERE id = ?",
                [$customerId]
            );
            
            apiResponse(true, [
                'redemption_id' => $result['redemption_id'],
                'points_redeemed' => $pointsAmount,
                'remaining_points' => (int)$updatedUser['points'],
                'redemption_value' => $pointsAmount * 10, // TSH value
                'redemption_type' => $redemptionType
            ], 'Points redeemed successfully');
        } else {
            apiError($result['message'], 400, 'REDEMPTION_FAILED');
        }
        
    } catch (Exception $e) {
        error_log('Redeem points error: ' . $e->getMessage());
        apiError('Failed to redeem points', 500, 'REDEEM_POINTS_ERROR');
    }
}

/**
 * Handle getting loyalty tier info
 */
function handleGetLoyaltyInfo($customerId) {
    try {
        $loyaltyTier = getCustomerLoyaltyTierEnhanced($customerId);
        
        // Get tier progression
        global $database;
        $allTiers = $database->fetchAll("
            SELECT name, min_points, points_multiplier, benefits
            FROM loyalty_tiers 
            ORDER BY min_points ASC
        ");
        
        $tierInfo = [
            'current_tier' => [
                'name' => $loyaltyTier['name'],
                'level' => $loyaltyTier['level'],
                'points_multiplier' => (float)$loyaltyTier['pointsMultiplier'],
                'benefits' => $loyaltyTier['benefits'] ?? []
            ],
            'next_tier' => $loyaltyTier['nextTier'] ?? null,
            'points_to_next' => (int)($loyaltyTier['pointsToNext'] ?? 0),
            'all_tiers' => $allTiers,
            'progress_percentage' => $loyaltyTier['progressPercentage'] ?? 0
        ];
        
        apiResponse(true, $tierInfo, 'Loyalty tier info retrieved successfully');
        
    } catch (Exception $e) {
        error_log('Loyalty info error: ' . $e->getMessage());
        apiError('Failed to get loyalty info', 500, 'LOYALTY_INFO_ERROR');
    }
}

/**
 * Handle getting referral info
 */
function handleGetReferralInfo($customerId) {
    try {
        $referralData = getCustomerReferralData($customerId);
        
        global $database;
        
        // Get recent referrals
        $recentReferrals = $database->fetchAll("
            SELECT 
                r.id,
                r.referred_user_id,
                u.name as referred_name,
                r.points_awarded,
                r.status,
                r.created_at
            FROM referrals r
            LEFT JOIN users u ON r.referred_user_id = u.id
            WHERE r.referrer_user_id = ?
            ORDER BY r.created_at DESC
            LIMIT 10
        ", [$customerId]);
        
        $referralInfo = [
            'referral_code' => $referralData['code'],
            'total_referrals' => (int)$referralData['totalReferrals'],
            'successful_referrals' => (int)($referralData['successfulReferrals'] ?? 0),
            'points_earned' => (int)$referralData['pointsEarned'],
            'bonus_rate' => (int)($referralData['bonusRate'] ?? 100),
            'share_url' => getBaseUrl() . '/register?ref=' . $referralData['code'],
            'recent_referrals' => $recentReferrals,
            'terms' => [
                'minimum_booking' => 50000, // TSH
                'points_per_referral' => 100,
                'referral_gets' => 50 // Points for the referred user
            ]
        ];
        
        apiResponse(true, $referralInfo, 'Referral info retrieved successfully');
        
    } catch (Exception $e) {
        error_log('Referral info error: ' . $e->getMessage());
        apiError('Failed to get referral info', 500, 'REFERRAL_INFO_ERROR');
    }
}
?>
