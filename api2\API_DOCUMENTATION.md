# Flix Salon & SPA Mobile API Documentation

## Overview

The Flix Salon & SPA Mobile API provides comprehensive endpoints for Flutter mobile application integration. This RESTful API supports customer authentication, service/package browsing, booking management, customer dashboard, wishlist, and rewards functionality.

**Base URL:** `https://yourdomain.com/api2/`

**API Version:** 2.0

**Authentication:** Session-based authentication with role-based access control

## Table of Contents

1. [Authentication](#authentication)
2. [Services API](#services-api)
3. [Packages API](#packages-api)
4. [Customer Panel API](#customer-panel-api)
5. [Bookings API](#bookings-api)
6. [Wishlist API](#wishlist-api)
7. [Rewards API](#rewards-api)
8. [Response Format](#response-format)
9. [Error Handling](#error-handling)
10. [Rate Limiting](#rate-limiting)

## Authentication

All API endpoints require authentication except for login and registration. The API uses session-based authentication with cookies.

### Base URL
```
GET /api2/auth/
```

### Login
**Endpoint:** `POST /api2/auth/login`

**Description:** Authenticate user and create session

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "password123"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "user": {
      "id": 123,
      "name": "John Doe",
      "email": "<EMAIL>",
      "role": "CUSTOMER",
      "points": 150,
      "avatar": "https://yourdomain.com/uploads/avatars/user123.jpg"
    },
    "session_id": "sess_abc123xyz"
  },
  "message": "Login successful"
}
```

### Register
**Endpoint:** `POST /api2/auth/register`

**Description:** Create new customer account

**Request Body:**
```json
{
  "name": "John Doe",
  "email": "<EMAIL>",
  "password": "password123",
  "phone": "+************",
  "referral_code": "REF123" // Optional
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "user": {
      "id": 124,
      "name": "John Doe",
      "email": "<EMAIL>",
      "role": "CUSTOMER",
      "points": 0,
      "referral_code": "JOHN124"
    }
  },
  "message": "Registration successful"
}
```

### Validate Session
**Endpoint:** `GET /api2/auth/validate`

**Description:** Check if current session is valid

**Response:**
```json
{
  "success": true,
  "data": {
    "valid": true,
    "user": {
      "id": 123,
      "name": "John Doe",
      "email": "<EMAIL>",
      "role": "CUSTOMER"
    }
  },
  "message": "Session is valid"
}
```

### Logout
**Endpoint:** `POST /api2/auth/logout`

**Description:** End current session

**Response:**
```json
{
  "success": true,
  "data": null,
  "message": "Logout successful"
}
```

### Password Reset
**Endpoint:** `POST /api2/auth/reset-password`

**Description:** Send password reset email

**Request Body:**
```json
{
  "email": "<EMAIL>"
}
```

**Response:**
```json
{
  "success": true,
  "data": null,
  "message": "Password reset email sent"
}
```

## Services API

### List Services
**Endpoint:** `GET /api2/services/`

**Description:** Get list of all active services with pagination

**Query Parameters:**
- `category` (string, optional): Filter by service category
- `limit` (integer, optional): Number of items per page (max 100, default 50)
- `offset` (integer, optional): Number of items to skip (default 0)
- `sort_by` (string, optional): Sort field (name, price, duration, category, created_at)
- `sort_order` (string, optional): Sort order (ASC, DESC)

**Example Request:**
```
GET /api2/services/?category=Hair&limit=20&sort_by=price&sort_order=ASC
```

**Response:**
```json
{
  "success": true,
  "data": {
    "services": [
      {
        "id": 1,
        "name": "Hair Cut & Style",
        "description": "Professional hair cutting and styling",
        "price": 0,
        "price_text": "Contact for pricing",
        "duration": 60,
        "category": "Hair",
        "image": "https://yourdomain.com/uploads/services/haircut.jpg",
        "is_featured": false
      }
    ],
    "pagination": {
      "total": 25,
      "limit": 20,
      "offset": 0,
      "has_more": true
    }
  },
  "message": "Services retrieved successfully"
}
```

### Get Service Details
**Endpoint:** `GET /api2/services/{id}`

**Description:** Get detailed information about a specific service

**Response:**
```json
{
  "success": true,
  "data": {
    "service": {
      "id": 1,
      "name": "Hair Cut & Style",
      "description": "Professional hair cutting and styling service...",
      "price": 0,
      "price_text": "Contact for pricing",
      "duration": 60,
      "category": "Hair",
      "image": "https://yourdomain.com/uploads/services/haircut.jpg",
      "is_featured": false,
      "variations": [
        {
          "id": 1,
          "name": "Basic Cut",
          "description": "Simple hair cut",
          "price": 0,
          "price_text": "Contact for pricing",
          "duration": 45
        }
      ],
      "has_variations": true
    }
  },
  "message": "Service retrieved successfully"
}
```

### Get Service Categories
**Endpoint:** `GET /api2/services/categories`

**Description:** Get list of all service categories

**Response:**
```json
{
  "success": true,
  "data": {
    "categories": [
      {
        "name": "Hair",
        "service_count": 15
      },
      {
        "name": "Spa",
        "service_count": 8
      }
    ],
    "subcategories": [
      {
        "id": 1,
        "name": "Hair Cutting",
        "category_id": 1,
        "category_name": "Hair"
      }
    ]
  },
  "message": "Categories retrieved successfully"
}
```

### Search Services
**Endpoint:** `GET /api2/services/search`

**Description:** Search services by various criteria

**Query Parameters:**
- `q` (string, optional): Search query for name/description
- `category` (string, optional): Filter by category
- `min_price` (number, optional): Minimum price filter
- `max_price` (number, optional): Maximum price filter
- `max_duration` (integer, optional): Maximum duration in minutes
- `limit` (integer, optional): Number of results (max 50, default 20)
- `offset` (integer, optional): Number of items to skip

**Example Request:**
```
GET /api2/services/search?q=massage&max_duration=90&limit=10
```

**Response:**
```json
{
  "success": true,
  "data": {
    "services": [...],
    "search_params": {
      "query": "massage",
      "category": "",
      "min_price": "",
      "max_price": "",
      "max_duration": "90"
    },
    "pagination": {
      "total": 5,
      "limit": 10,
      "offset": 0,
      "has_more": false
    }
  },
  "message": "Search completed successfully"
}
```

## Packages API

### List Packages
**Endpoint:** `GET /api2/packages/`

**Description:** Get list of all active packages with calculated savings

**Query Parameters:**
- `limit` (integer, optional): Number of items per page (max 100, default 50)
- `offset` (integer, optional): Number of items to skip
- `sort_by` (string, optional): Sort field (name, price, package_duration, created_at)
- `sort_order` (string, optional): Sort order (ASC, DESC)

**Response:**
```json
{
  "success": true,
  "data": {
    "packages": [
      {
        "id": 1,
        "name": "Spa Relaxation Package",
        "description": "Complete relaxation experience",
        "price": 0,
        "price_text": "Contact for pricing",
        "duration": 180,
        "image": "https://yourdomain.com/uploads/packages/spa-package.jpg",
        "savings": 0,
        "discount_percentage": 0,
        "service_count": 3
      }
    ],
    "pagination": {
      "total": 8,
      "limit": 50,
      "offset": 0,
      "has_more": false
    }
  },
  "message": "Packages retrieved successfully"
}
```

### Get Package Details
**Endpoint:** `GET /api2/packages/{id}`

**Description:** Get detailed package information including included services

**Response:**
```json
{
  "success": true,
  "data": {
    "package": {
      "id": 1,
      "name": "Spa Relaxation Package",
      "description": "Complete relaxation experience with multiple services",
      "price": 0,
      "price_text": "Contact for pricing",
      "duration": 180,
      "image": "https://yourdomain.com/uploads/packages/spa-package.jpg",
      "savings": 0,
      "discount_percentage": 0,
      "validity_days": 30,
      "services": [
        {
          "id": 5,
          "name": "Full Body Massage",
          "description": "Relaxing full body massage",
          "price": 0,
          "duration": 90,
          "category": "Spa"
        }
      ]
    }
  },
  "message": "Package retrieved successfully"
}
```

### Search Packages
**Endpoint:** `GET /api2/packages/search`

**Description:** Search packages by various criteria

**Query Parameters:**
- `q` (string, optional): Search query
- `min_price` (number, optional): Minimum price
- `max_price` (number, optional): Maximum price
- `max_duration` (integer, optional): Maximum duration
- `limit` (integer, optional): Results limit
- `offset` (integer, optional): Results offset

## Customer Panel API

**Authentication Required:** All endpoints require customer authentication

### Get Dashboard
**Endpoint:** `GET /api2/customer/dashboard`

**Description:** Get comprehensive customer dashboard data

**Response:**
```json
{
  "success": true,
  "data": {
    "profile": {
      "id": 123,
      "name": "John Doe",
      "email": "<EMAIL>",
      "phone": "+************",
      "points": 150,
      "avatar": "https://yourdomain.com/uploads/avatars/user123.jpg",
      "member_since": "2024-01-15 10:30:00"
    },
    "upcoming_bookings": [...],
    "recent_bookings": [...],
    "points_data": {
      "current_points": 150,
      "total_earned": 500,
      "total_redeemed": 350,
      "recent_transactions": [...]
    },
    "loyalty_tier": {
      "name": "Silver",
      "level": 2,
      "points_multiplier": 1.5,
      "next_tier": "Gold",
      "points_to_next": 350
    },
    "stats": {
      "total_bookings": 12,
      "completed_bookings": 10,
      "total_spent": 450000,
      "favorite_service": "Hair Cut & Style",
      "last_visit": "2024-06-15"
    }
  },
  "message": "Dashboard data retrieved successfully"
}
```

### Get Profile
**Endpoint:** `GET /api2/customer/profile`

**Description:** Get customer profile information

**Response:**
```json
{
  "success": true,
  "data": {
    "profile": {
      "id": 123,
      "name": "John Doe",
      "email": "<EMAIL>",
      "phone": "+************",
      "date_of_birth": "1990-05-15",
      "points": 150,
      "referral_code": "JOHN123",
      "avatar": "https://yourdomain.com/uploads/avatars/user123.jpg",
      "member_since": "2024-01-15 10:30:00"
    }
  },
  "message": "Profile retrieved successfully"
}
```

### Update Profile
**Endpoint:** `PUT /api2/customer/profile`

**Description:** Update customer profile information

**Request Body:**
```json
{
  "name": "John Smith",
  "phone": "+255987654321",
  "date_of_birth": "1990-05-15"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "profile": {
      "id": 123,
      "name": "John Smith",
      "email": "<EMAIL>",
      "phone": "+255987654321",
      "date_of_birth": "1990-05-15",
      "points": 150,
      "referral_code": "JOHN123",
      "avatar": "https://yourdomain.com/uploads/avatars/user123.jpg",
      "member_since": "2024-01-15 10:30:00"
    }
  },
  "message": "Profile updated successfully"
}
```

### Get Customer Bookings
**Endpoint:** `GET /api2/customer/bookings`

**Description:** Get customer's booking history

**Query Parameters:**
- `status` (string, optional): Filter by status (PENDING, CONFIRMED, COMPLETED, CANCELLED)
- `limit` (integer, optional): Number of results (max 50, default 20)
- `offset` (integer, optional): Results offset

**Response:**
```json
{
  "success": true,
  "data": {
    "bookings": [
      {
        "id": 456,
        "service_name": "Hair Cut & Style",
        "package_name": null,
        "staff_name": "Jane Smith",
        "date": "2024-07-10",
        "start_time": "14:00",
        "end_time": "15:00",
        "status": "CONFIRMED",
        "total_amount": 50000,
        "points_used": 0,
        "payment_status": "PAID"
      }
    ],
    "pagination": {
      "total": 12,
      "limit": 20,
      "offset": 0,
      "has_more": false
    }
  },
  "message": "Bookings retrieved successfully"
}
```

### Get Points Data
**Endpoint:** `GET /api2/customer/points`

**Description:** Get detailed points and loyalty information

### Get Statistics
**Endpoint:** `GET /api2/customer/stats`

**Description:** Get customer statistics and analytics

## Bookings API

**Authentication Required:** All endpoints require customer authentication

### Create Booking
**Endpoint:** `POST /api2/bookings/`

**Description:** Create a new booking

**Request Body:**
```json
{
  "service_id": 1,
  "service_variation_id": null,
  "package_id": null,
  "staff_id": 5,
  "date": "2024-07-15",
  "start_time": "14:00",
  "notes": "Please call before arrival",
  "points_used": 50
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "booking": {
      "id": 789,
      "service_name": "Hair Cut & Style",
      "staff_name": "Jane Smith",
      "date": "2024-07-15",
      "start_time": "14:00",
      "end_time": "15:00",
      "status": "PENDING",
      "total_amount": 45000,
      "points_used": 50,
      "notes": "Please call before arrival"
    }
  },
  "message": "Booking created successfully"
}
```

### Get Booking Details
**Endpoint:** `GET /api2/bookings/{id}`

**Description:** Get detailed information about a specific booking

### Update Booking
**Endpoint:** `PUT /api2/bookings/{id}`

**Description:** Update booking details (limited fields)

**Request Body:**
```json
{
  "notes": "Updated notes"
}
```

### Cancel Booking
**Endpoint:** `DELETE /api2/bookings/{id}`

**Description:** Cancel a booking (must be at least 24 hours in advance)

**Response:**
```json
{
  "success": true,
  "data": {
    "booking": {
      "id": 789,
      "status": "CANCELLED",
      "cancellation_reason": "Customer request"
    }
  },
  "message": "Booking cancelled successfully"
}
```

### Check Availability
**Endpoint:** `GET /api2/bookings/availability`

**Description:** Check if a time slot is available

**Query Parameters:**
- `staff_id` (integer, required): Staff member ID
- `date` (string, required): Date in YYYY-MM-DD format
- `start_time` (string, required): Start time in HH:MM format
- `end_time` (string, required): End time in HH:MM format

**Example Request:**
```
GET /api2/bookings/availability?staff_id=5&date=2024-07-15&start_time=14:00&end_time=15:00
```

**Response:**
```json
{
  "success": true,
  "data": {
    "available": true,
    "staff_id": "5",
    "date": "2024-07-15",
    "start_time": "14:00",
    "end_time": "15:00"
  },
  "message": "Time slot is available"
}
```

### Get Time Slots
**Endpoint:** `GET /api2/bookings/time-slots`

**Description:** Get available time slots for a specific date and staff

**Query Parameters:**
- `staff_id` (integer, required): Staff member ID
- `date` (string, required): Date in YYYY-MM-DD format
- `duration` (integer, optional): Service duration in minutes (default 60)

**Response:**
```json
{
  "success": true,
  "data": {
    "time_slots": [
      {
        "start_time": "09:00",
        "end_time": "10:00",
        "available": true
      },
      {
        "start_time": "09:30",
        "end_time": "10:30",
        "available": false
      }
    ],
    "staff_id": "5",
    "date": "2024-07-15",
    "duration": 60
  },
  "message": "Time slots retrieved successfully"
}
```

## Wishlist API

**Authentication Required:** All endpoints require customer authentication

### Get Wishlist
**Endpoint:** `GET /api2/wishlist/`

**Description:** Get customer's wishlist items

**Query Parameters:**
- `limit` (integer, optional): Number of results (max 100, default 50)
- `offset` (integer, optional): Results offset

**Response:**
```json
{
  "success": true,
  "data": {
    "wishlist_items": [
      {
        "wishlist_id": 123,
        "type": "service",
        "item": {
          "id": 1,
          "name": "Hair Cut & Style",
          "description": "Professional hair cutting",
          "price": 0,
          "duration": 60,
          "category": "Hair",
          "image": "https://yourdomain.com/uploads/services/haircut.jpg"
        },
        "added_at": "2024-06-15 10:30:00"
      }
    ],
    "pagination": {
      "total": 5,
      "limit": 50,
      "offset": 0,
      "has_more": false
    }
  },
  "message": "Wishlist retrieved successfully"
}
```

### Add to Wishlist
**Endpoint:** `POST /api2/wishlist/`

**Description:** Add item to wishlist

**Request Body:**
```json
{
  "item_type": "service",
  "item_id": 1
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "wishlist_count": 6,
    "item_name": "Hair Cut & Style"
  },
  "message": "Item added to wishlist successfully"
}
```

### Toggle Wishlist
**Endpoint:** `POST /api2/wishlist/toggle`

**Description:** Add or remove item from wishlist

**Request Body:**
```json
{
  "item_type": "service",
  "item_id": 1
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "action": "added",
    "in_wishlist": true,
    "wishlist_count": 6
  },
  "message": "Item added successfully"
}
```

### Remove from Wishlist
**Endpoint:** `DELETE /api2/wishlist/{wishlist_id}`

**Description:** Remove specific item from wishlist

**Response:**
```json
{
  "success": true,
  "data": {
    "wishlist_count": 5
  },
  "message": "Item removed from wishlist successfully"
}
```

## Rewards API

**Authentication Required:** All endpoints require customer authentication

### Get Rewards Overview
**Endpoint:** `GET /api2/rewards/`

**Description:** Get comprehensive rewards and loyalty overview

**Response:**
```json
{
  "success": true,
  "data": {
    "points": {
      "current": 150,
      "total_earned": 500,
      "total_redeemed": 350,
      "pending": 0
    },
    "loyalty_tier": {
      "name": "Silver",
      "level": 2,
      "points_multiplier": 1.5,
      "next_tier": "Gold",
      "points_to_next": 350,
      "benefits": ["10% discount", "Priority booking"]
    },
    "referral": {
      "code": "JOHN123",
      "total_referrals": 3,
      "points_earned": 300,
      "bonus_rate": 100
    },
    "recent_transactions": [...]
  },
  "message": "Rewards overview retrieved successfully"
}
```

### Get Points Details
**Endpoint:** `GET /api2/rewards/points`

**Description:** Get detailed points information and breakdown

**Response:**
```json
{
  "success": true,
  "data": {
    "current_points": 150,
    "total_earned": 500,
    "total_redeemed": 350,
    "pending_points": 0,
    "points_value": {
      "currency": "TSH",
      "rate": 10,
      "current_value": 1500
    },
    "breakdown": [
      {
        "transaction_type": "booking_completion",
        "earned": 300,
        "spent": 0,
        "transaction_count": 6
      }
    ],
    "expiry_info": {
      "points_expire": true,
      "expiry_period": "12 months",
      "next_expiry_date": null,
      "expiring_points": 0
    }
  },
  "message": "Points details retrieved successfully"
}
```

### Get Points Transactions
**Endpoint:** `GET /api2/rewards/transactions`

**Description:** Get points transaction history

**Query Parameters:**
- `type` (string, optional): Filter by type ('earned' or 'redeemed')
- `limit` (integer, optional): Number of results (max 50, default 20)
- `offset` (integer, optional): Results offset

**Response:**
```json
{
  "success": true,
  "data": {
    "transactions": [
      {
        "id": 789,
        "points": 50,
        "type": "booking_completion",
        "description": "Points earned from booking completion",
        "booking_id": 456,
        "date": "2024-06-15 16:30:00",
        "is_credit": true
      }
    ],
    "pagination": {
      "total": 25,
      "limit": 20,
      "offset": 0,
      "has_more": true
    }
  },
  "message": "Points transactions retrieved successfully"
}
```

### Redeem Points
**Endpoint:** `POST /api2/rewards/redeem`

**Description:** Redeem points for discounts or rewards

**Request Body:**
```json
{
  "points_amount": 100,
  "redemption_type": "booking_discount",
  "booking_id": 456
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "redemption_id": "RED123",
    "points_redeemed": 100,
    "remaining_points": 50,
    "redemption_value": 1000,
    "redemption_type": "booking_discount"
  },
  "message": "Points redeemed successfully"
}
```

### Get Loyalty Info
**Endpoint:** `GET /api2/rewards/loyalty`

**Description:** Get detailed loyalty tier information

**Response:**
```json
{
  "success": true,
  "data": {
    "current_tier": {
      "name": "Silver",
      "level": 2,
      "points_multiplier": 1.5,
      "benefits": ["10% discount", "Priority booking"]
    },
    "next_tier": {
      "name": "Gold",
      "min_points": 1000,
      "points_multiplier": 2.0
    },
    "points_to_next": 350,
    "all_tiers": [...],
    "progress_percentage": 65
  },
  "message": "Loyalty tier info retrieved successfully"
}
```

### Get Referral Info
**Endpoint:** `GET /api2/rewards/referral`

**Description:** Get referral program information

**Response:**
```json
{
  "success": true,
  "data": {
    "referral_code": "JOHN123",
    "total_referrals": 3,
    "successful_referrals": 2,
    "points_earned": 200,
    "bonus_rate": 100,
    "share_url": "https://yourdomain.com/register?ref=JOHN123",
    "recent_referrals": [...],
    "terms": {
      "minimum_booking": 50000,
      "points_per_referral": 100,
      "referral_gets": 50
    }
  },
  "message": "Referral info retrieved successfully"
}
```

## Response Format

All API responses follow a consistent JSON format:

```json
{
  "success": boolean,
  "data": object|array|null,
  "message": string,
  "error_code": string (only on errors),
  "timestamp": string (ISO 8601 format)
}
```

### Success Response
```json
{
  "success": true,
  "data": {
    "key": "value"
  },
  "message": "Operation completed successfully",
  "timestamp": "2024-07-03T10:30:00Z"
}
```

### Error Response
```json
{
  "success": false,
  "data": null,
  "message": "Error description",
  "error_code": "ERROR_CODE",
  "timestamp": "2024-07-03T10:30:00Z"
}
```

## Error Handling

### HTTP Status Codes

- `200` - Success
- `201` - Created
- `400` - Bad Request
- `401` - Unauthorized
- `403` - Forbidden
- `404` - Not Found
- `405` - Method Not Allowed
- `409` - Conflict
- `500` - Internal Server Error

### Common Error Codes

- `AUTHENTICATION_REQUIRED` - User must be logged in
- `CUSTOMER_ACCESS_REQUIRED` - Endpoint requires customer role
- `INVALID_REQUEST_DATA` - Request body validation failed
- `MISSING_REQUIRED_FIELDS` - Required fields are missing
- `RESOURCE_NOT_FOUND` - Requested resource doesn't exist
- `INSUFFICIENT_PERMISSIONS` - User lacks required permissions
- `RATE_LIMIT_EXCEEDED` - Too many requests
- `SERVICE_UNAVAILABLE` - Service temporarily unavailable

### Validation Errors

Field validation errors include specific details:

```json
{
  "success": false,
  "data": {
    "validation_errors": {
      "email": ["Email format is invalid"],
      "password": ["Password must be at least 8 characters"]
    }
  },
  "message": "Validation failed",
  "error_code": "VALIDATION_ERROR"
}
```

## Rate Limiting

The API implements rate limiting to ensure fair usage:

- **Authentication endpoints:** 10 requests per minute per IP
- **General endpoints:** 100 requests per minute per authenticated user
- **Search endpoints:** 30 requests per minute per user

Rate limit headers are included in responses:

```
X-RateLimit-Limit: 100
X-RateLimit-Remaining: 95
X-RateLimit-Reset: 1625097600
```

## Security Considerations

1. **HTTPS Only:** All API calls must use HTTPS
2. **Session Management:** Sessions expire after 24 hours of inactivity
3. **Input Validation:** All inputs are validated and sanitized
4. **CORS:** Configured for mobile app domains only
5. **SQL Injection Protection:** All queries use prepared statements
6. **XSS Protection:** Output is properly escaped

## Mobile App Integration

### Flutter/Dart Example

```dart
import 'package:http/http.dart' as http;
import 'dart:convert';

class FlixApiClient {
  static const String baseUrl = 'https://yourdomain.com/api2';

  Future<Map<String, dynamic>> login(String email, String password) async {
    final response = await http.post(
      Uri.parse('$baseUrl/auth/login'),
      headers: {'Content-Type': 'application/json'},
      body: jsonEncode({
        'email': email,
        'password': password,
      }),
    );

    return jsonDecode(response.body);
  }

  Future<Map<String, dynamic>> getServices() async {
    final response = await http.get(
      Uri.parse('$baseUrl/services/'),
      headers: {'Content-Type': 'application/json'},
    );

    return jsonDecode(response.body);
  }
}
```

## Support

For API support and questions:
- Email: <EMAIL>
- Documentation: https://yourdomain.com/api2/
- Status Page: https://status.flixsalon.com

---

**Last Updated:** July 3, 2024
**API Version:** 2.0
